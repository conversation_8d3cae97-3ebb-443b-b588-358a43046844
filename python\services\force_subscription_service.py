"""
Force Subscription service for managing mandatory channel subscriptions
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from config.database import get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp
from telegram import Bo<PERSON>
from telegram.error import TelegramError

logger = logging.getLogger(__name__)

class ForceSubscriptionService:
    """Service for force subscription operations"""

    def __init__(self, bot: Bot = None):
        if bot:
            self.bot = bot
            self._bot_initialized = True
        else:
            # Create bot instance if not provided (for backward compatibility)
            self.bot = Bot(settings.BOT_TOKEN)
            self._bot_initialized = False

    async def _ensure_bot_initialized(self):
        """Ensure bot instance is properly initialized"""
        if not self._bot_initialized:
            try:
                # Initialize the bot by calling get_me
                await self.bot.initialize()
                self._bot_initialized = True
            except Exception as e:
                logger.error(f"Failed to initialize bot: {e}")
                raise RuntimeError("Bot initialization failed")
    
    async def get_force_subscription_channels(self) -> List[Dict[str, Any]]:
        """Get all force subscription channels (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['force_channels'])
            
            cursor = collection.find({})
            channels = await cursor.to_list(length=None)
            
            # Convert ObjectId to string and ensure proper format
            for channel in channels:
                if '_id' in channel:
                    del channel['_id']
            
            return channels
            
        except Exception as e:
            logger.error(f"Error getting force subscription channels: {e}")
            return []
    
    async def add_force_subscription_channel(self, channel_data: Dict[str, Any]) -> bool:
        """Add force subscription channel (matching PHP logic exactly)"""
        try:
            collection = await get_collection(COLLECTIONS['force_channels'])
            
            # Check if channel already exists
            existing_channel = await collection.find_one({
                "$or": [
                    {"channel_id": channel_data.get('channel_id')},
                    {"username": channel_data.get('username')}
                ]
            })
            
            if existing_channel:
                return False  # Channel already exists
            
            # Add timestamps
            channel_data['created_at'] = get_current_timestamp()
            channel_data['updated_at'] = get_current_timestamp()
            
            result = await collection.insert_one(channel_data)
            
            return result.inserted_id is not None
            
        except Exception as e:
            logger.error(f"Error adding force subscription channel: {e}")
            return False
    
    async def remove_force_subscription_channel(self, channel_identifier: str) -> bool:
        """Remove force subscription channel by ID or username"""
        try:
            collection = await get_collection(COLLECTIONS['force_channels'])
            
            # Try to remove by channel_id or username
            result = await collection.delete_one({
                "$or": [
                    {"channel_id": channel_identifier},
                    {"username": channel_identifier}
                ]
            })
            
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Error removing force subscription channel: {e}")
            return False
    
    async def check_user_subscriptions(self, user_id: int) -> Dict[str, Any]:
        """Check if user is subscribed to all force subscription channels (matching PHP logic exactly)"""
        try:
            # Ensure bot is initialized
            await self._ensure_bot_initialized()

            channels = await self.get_force_subscription_channels()
            unsubscribed_channels = []

            # If no force subscription channels configured, check main channel as fallback (like PHP)
            if not channels:
                from config.settings import settings
                main_channel = getattr(settings, 'MAIN_CHANNEL', None)

                if main_channel:
                    try:
                        # Check main channel membership
                        check_identifier = f"@{main_channel}" if not main_channel.startswith('@') else main_channel
                        member = await self.bot.get_chat_member(check_identifier, user_id)

                        if member.status in ['left', 'kicked']:
                            # Create a virtual channel object for main channel
                            main_channel_obj = {
                                'channel_id': None,
                                'username': main_channel,
                                'title': 'Main Channel',
                                'type': 'main_channel'
                            }
                            unsubscribed_channels.append(main_channel_obj)

                    except TelegramError as e:
                        logger.error(f"Error checking main channel subscription: {e}")
                        # If we can't check main channel, assume user is not subscribed
                        main_channel_obj = {
                            'channel_id': None,
                            'username': main_channel,
                            'title': 'Main Channel',
                            'type': 'main_channel'
                        }
                        unsubscribed_channels.append(main_channel_obj)

                return {
                    'all_subscribed': len(unsubscribed_channels) == 0,
                    'unsubscribed_channels': unsubscribed_channels,
                    'subscription_required': main_channel is not None
                }

            # Check all configured force subscription channels
            for channel in channels:
                channel_id = channel.get('channel_id')
                username = channel.get('username')

                # Determine which identifier to use for checking
                if channel_id:
                    check_identifier = str(channel_id)
                elif username:
                    check_identifier = f"@{username}" if not username.startswith('@') else username
                else:
                    logger.warning(f"Channel has no valid identifier: {channel}")
                    unsubscribed_channels.append(channel)
                    continue

                try:
                    member = await self.bot.get_chat_member(check_identifier, user_id)

                    # Check if user is subscribed (member, administrator, or creator)
                    if member.status in ['left', 'kicked']:
                        unsubscribed_channels.append(channel)

                except TelegramError as e:
                    logger.error(f"Error checking subscription for channel {check_identifier}: {e}")
                    # If we can't check, assume user is not subscribed
                    unsubscribed_channels.append(channel)

            return {
                'all_subscribed': len(unsubscribed_channels) == 0,
                'unsubscribed_channels': unsubscribed_channels,
                'subscription_required': len(channels) > 0
            }

        except Exception as e:
            logger.error(f"Error checking user subscriptions: {e}")
            return {
                'all_subscribed': True,
                'unsubscribed_channels': [],
                'subscription_required': False
            }
    
    async def get_subscription_message_and_keyboard(self, unsubscribed_channels: List[Dict[str, Any]]):
        """Generate subscription message and keyboard for unsubscribed channels (matching PHP logic exactly)"""
        try:
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            if not unsubscribed_channels:
                # No unsubscribed channels, return success message
                message = "✅ <b>All Channels Joined</b>\n\nYou have joined all required channels!"
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('💰GET MONEY💰', callback_data='joined')]
                ])
                return message, keyboard

            # Check if this is main channel only (fallback case) - matching PHP exactly
            if len(unsubscribed_channels) == 1 and unsubscribed_channels[0].get('type') == 'main_channel':
                main_channel = unsubscribed_channels[0].get('username', 'YourChannel')

                # Simple error message exactly like PHP
                message = f"<i>🚫 You must join our channel before you can get money.</i>\n\n"
                message += f"👇 <b>Join our channel first:</b>\n"
                message += f"🔗 <a href=\"https://t.me/{main_channel}\">Click here to join</a>\n\n"
                message += f"After joining, click the button below to continue:"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('✅ I joined, check again', callback_data='joined')]
                ])
                return message, keyboard

            # Multiple force subscription channels
            message = "<i>🚫 You must join all required channels before you can get money.</i>\n\n"
            message += "👇 <b>Please join these channels:</b>\n\n"

            keyboard_buttons = []
            
            for i, channel in enumerate(unsubscribed_channels, 1):
                title = channel.get('title', channel.get('username', 'Unknown Channel'))
                username = channel.get('username')
                invite_link = channel.get('invite_link')
                
                message += f"{i}. {title}\n"
                
                # Create join button
                if invite_link:
                    join_url = invite_link
                elif username:
                    join_url = f"https://t.me/{username}"
                else:
                    continue  # Skip if no way to join
                
                keyboard_buttons.append([
                    InlineKeyboardButton(f"📢 Join {title}", url=join_url)
                ])
            
            message += "\nAfter joining all channels, click the button below to continue:"
            
            # Add check subscription button
            keyboard_buttons.append([
                InlineKeyboardButton("✅ I joined, check again", callback_data="checkSubscription")
            ])
            
            keyboard = InlineKeyboardMarkup(keyboard_buttons)
            
            return message, keyboard
            
        except Exception as e:
            logger.error(f"Error generating subscription message: {e}")
            return "🔔 Subscription required. Please join all required channels.", None
    
    async def parse_channel_input(self, channel_input: str) -> Optional[Dict[str, Any]]:
        """Parse channel input and get channel information"""
        try:
            channel_input = channel_input.strip()
            
            # Try to get chat information
            try:
                chat = await self.bot.get_chat(channel_input)
                
                channel_data = {
                    'channel_id': str(chat.id),
                    'title': chat.title,
                    'type': chat.type,
                    'username': chat.username,
                    'invite_link': None
                }
                
                # Try to get invite link if it's a private channel
                if chat.type in ['channel', 'supergroup'] and not chat.username:
                    try:
                        invite_link = await self.bot.export_chat_invite_link(chat.id)
                        channel_data['invite_link'] = invite_link
                    except TelegramError:
                        pass  # Can't get invite link
                
                return channel_data
                
            except TelegramError as e:
                logger.error(f"Error getting chat info for {channel_input}: {e}")
                return None
            
        except Exception as e:
            logger.error(f"Error parsing channel input: {e}")
            return None
    
    async def is_force_subscription_enabled(self) -> bool:
        """Check if force subscription is enabled"""
        try:
            channels = await self.get_force_subscription_channels()
            return len(channels) > 0
            
        except Exception as e:
            logger.error(f"Error checking force subscription status: {e}")
            return False
    
    async def format_channels_list_message(self) -> str:
        """Format channels list message for admin"""
        try:
            channels = await self.get_force_subscription_channels()
            
            if not channels:
                message = "🔔 <b>Force Subscription Channels</b>\n\n"
                message += "❌ No channels configured.\n\n"
                message += "Add channels to require users to join them before using the bot."
                return message
            
            message = "🔔 <b>Force Subscription Channels</b>\n\n"
            message += f"📊 <b>Total Channels:</b> {len(channels)}\n\n"
            
            for i, channel in enumerate(channels, 1):
                title = channel.get('title', 'Unknown Channel')
                username = channel.get('username', 'No username')
                channel_type = channel.get('type', 'unknown')
                
                message += f"<b>{i}.</b> {title}\n"
                message += f"   📱 @{username}\n"
                message += f"   🔗 Type: {channel_type.title()}\n\n"
            
            message += "💡 <b>Tip:</b> Users must join all channels to use the bot"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting channels list: {e}")
            return "🔔 <b>Force Subscription Channels</b>\n\n❌ Error loading channels."
    
    async def create_channels_management_keyboard(self):
        """Create channels management keyboard"""
        try:
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            
            keyboard_buttons = [
                [InlineKeyboardButton('➕ Add Channel', callback_data='addForceChannel')],
                [InlineKeyboardButton('➖ Remove Channel', callback_data='removeForceChannel')],
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ]
            
            return InlineKeyboardMarkup(keyboard_buttons)
            
        except Exception as e:
            logger.error(f"Error creating channels management keyboard: {e}")
            # Return basic keyboard if there's an error
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

    # ==================== NEW ADMIN FORCESUB MANAGEMENT ====================

    async def get_forcesub_statistics(self) -> Dict[str, Any]:
        """Get comprehensive force subscribe statistics for admin panel"""
        try:
            channels = await self.get_force_subscription_channels()

            # Get main channel info
            main_channel = None
            for channel in channels:
                if channel.get('is_main_channel', False):
                    main_channel = channel
                    break

            return {
                'total_channels': len(channels),
                'main_channel': main_channel,
                'channels_list': channels
            }

        except Exception as e:
            logger.error(f"Error getting forcesub statistics: {e}")
            return {
                'total_channels': 0,
                'main_channel': None,
                'channels_list': []
            }

    async def set_main_channel(self, channel_input: str, admin_user_id: int) -> tuple:
        """Set main channel with validation"""
        try:
            # Parse and validate channel
            channel_data = await self.parse_channel_input(channel_input)
            if not channel_data:
                return False, "❌ Invalid channel. Please check the channel username or invite link."

            # Check if bot has admin access
            try:
                chat_member = await self.bot.get_chat_member(channel_data['channel_id'], self.bot.id)
                if chat_member.status not in ['administrator', 'creator']:
                    return False, "❌ Bot must be an administrator in the channel."
            except TelegramError:
                return False, "❌ Cannot access channel. Make sure the bot is added as admin."

            collection = await get_collection(COLLECTIONS['force_channels'])

            # Remove main channel flag from all existing channels
            await collection.update_many(
                {"is_main_channel": True},
                {"$set": {"is_main_channel": False}}
            )

            # Check if channel already exists
            existing_channel = await collection.find_one({
                "$or": [
                    {"channel_id": channel_data['channel_id']},
                    {"username": channel_data.get('username')}
                ]
            })

            if existing_channel:
                # Update existing channel to be main channel
                await collection.update_one(
                    {"_id": existing_channel["_id"]},
                    {"$set": {"is_main_channel": True, "updated_at": get_current_timestamp()}}
                )
            else:
                # Add new channel as main channel
                channel_data.update({
                    'is_main_channel': True,
                    'added_by': admin_user_id,
                    'added_at': get_current_timestamp(),
                    'created_at': get_current_timestamp(),
                    'updated_at': get_current_timestamp()
                })
                await collection.insert_one(channel_data)

            channel_name = channel_data.get('title', channel_data.get('username', 'Unknown'))
            return True, f"✅ Main channel set successfully!\n\nChannel: {channel_name}\nMembers will be redirected here for joining."

        except Exception as e:
            logger.error(f"Error setting main channel: {e}")
            return False, "❌ Something went wrong while setting main channel."

    async def add_channel_admin(self, channel_input: str, admin_user_id: int) -> tuple:
        """Add channel with admin validation"""
        try:
            # Parse and validate channel
            channel_data = await self.parse_channel_input(channel_input)
            if not channel_data:
                return False, "❌ Invalid channel. Please check the channel username or invite link."

            # Check if bot has admin access
            try:
                chat_member = await self.bot.get_chat_member(channel_data['channel_id'], self.bot.id)
                if chat_member.status not in ['administrator', 'creator']:
                    return False, "❌ Bot must be an administrator in the channel."
            except TelegramError:
                return False, "❌ Cannot access channel. Make sure the bot is added as admin."

            collection = await get_collection(COLLECTIONS['force_channels'])

            # Check if channel already exists
            existing_channel = await collection.find_one({
                "$or": [
                    {"channel_id": channel_data['channel_id']},
                    {"username": channel_data.get('username')}
                ]
            })

            if existing_channel:
                return False, "❌ Channel is already added to force subscribe list."

            # Determine channel type
            channel_type = "Public" if channel_data.get('username') else "Private"

            # Add channel
            channel_data.update({
                'is_main_channel': False,
                'added_by': admin_user_id,
                'added_at': get_current_timestamp(),
                'created_at': get_current_timestamp(),
                'updated_at': get_current_timestamp()
            })

            result = await collection.insert_one(channel_data)

            if result.inserted_id:
                channel_name = channel_data.get('title', channel_data.get('username', 'Unknown'))
                return True, f"✅ Channel added successfully!\n\nChannel: {channel_name}\nType: {channel_type}\nUsers must now join this channel."
            else:
                return False, "❌ Failed to add channel to database."

        except Exception as e:
            logger.error(f"Error adding channel: {e}")
            return False, "❌ Something went wrong while adding channel."

    async def add_channel_from_forwarded_message(self, channel_id: int, channel_title: str, channel_username: str, admin_user_id: int) -> tuple:
        """Add channel from forwarded message with validation"""
        try:
            # Check if bot has admin access
            try:
                chat_member = await self.bot.get_chat_member(channel_id, self.bot.id)
                if chat_member.status not in ['administrator', 'creator']:
                    return False, "❌ Bot must be an administrator in the channel."
            except TelegramError:
                return False, "❌ Cannot access channel. Make sure the bot is added as admin."

            collection = await get_collection(COLLECTIONS['force_channels'])

            # Check if channel already exists
            existing_channel = await collection.find_one({
                "$or": [
                    {"channel_id": channel_id},
                    {"username": channel_username} if channel_username else {"channel_id": channel_id}
                ]
            })

            if existing_channel:
                return False, "❌ Channel is already added to force subscribe list."

            # Determine channel type
            channel_type = "Public" if channel_username else "Private"

            # Create channel data
            channel_data = {
                'channel_id': channel_id,
                'title': channel_title,
                'username': channel_username,
                'type': channel_type.lower(),
                'is_main_channel': False,
                'added_by': admin_user_id,
                'added_at': get_current_timestamp(),
                'created_at': get_current_timestamp(),
                'updated_at': get_current_timestamp()
            }

            # Add channel
            result = await collection.insert_one(channel_data)

            if result.inserted_id:
                success_message = f"✅ <b>Channel Added Successfully!</b>\n\n"
                success_message += f"📢 <b>Channel:</b> {channel_title}\n"
                success_message += f"🆔 <b>ID:</b> {channel_id}\n"
                success_message += f"📱 <b>Type:</b> {channel_type}\n"
                if channel_username:
                    success_message += f"🔗 <b>Username:</b> @{channel_username}\n"
                success_message += f"\n🎉 Users must now join this channel to use the bot!"

                return True, success_message
            else:
                return False, "❌ Failed to add channel to database."

        except Exception as e:
            logger.error(f"Error adding channel from forwarded message: {e}")
            return False, f"❌ Error adding channel: {str(e)}"

    async def get_channels_for_removal(self) -> List[Dict[str, Any]]:
        """Get channels list for removal selection"""
        try:
            channels = await self.get_force_subscription_channels()

            # Format channels for selection
            formatted_channels = []
            for i, channel in enumerate(channels, 1):
                channel_name = channel.get('title', channel.get('username', 'Unknown'))
                main_indicator = " (Main)" if channel.get('is_main_channel', False) else ""
                formatted_channels.append({
                    'number': i,
                    'channel': channel,
                    'display_name': f"{channel_name}{main_indicator}"
                })

            return formatted_channels

        except Exception as e:
            logger.error(f"Error getting channels for removal: {e}")
            return []

    async def remove_channel_by_number(self, channel_number: int, admin_user_id: int) -> tuple:
        """Remove channel by selection number"""
        try:
            channels = await self.get_force_subscription_channels()

            if channel_number < 1 or channel_number > len(channels):
                return False, "❌ Invalid channel number."

            # Get the channel to remove (0-based index)
            channel_to_remove = channels[channel_number - 1]
            channel_name = channel_to_remove.get('title', channel_to_remove.get('username', 'Unknown'))

            collection = await get_collection(COLLECTIONS['force_channels'])

            # Remove the channel
            result = await collection.delete_one({
                "$or": [
                    {"channel_id": channel_to_remove.get('channel_id')},
                    {"username": channel_to_remove.get('username')}
                ]
            })

            if result.deleted_count > 0:
                return True, f"✅ Channel removed successfully!\n\nChannel: {channel_name} is no longer required for joining."
            else:
                return False, "❌ Failed to remove channel from database."

        except Exception as e:
            logger.error(f"Error removing channel: {e}")
            return False, "❌ Something went wrong while removing channel."

    async def format_admin_channels_message(self) -> str:
        """Format channels message for admin panel"""
        try:
            stats = await self.get_forcesub_statistics()

            message = "⚙️ Hello, Welcome To The Channel Settings.\n\n"
            message += f"🏷️ Total Channels In Check: {stats['total_channels']}\n\n"

            if stats['channels_list']:
                for i, channel in enumerate(stats['channels_list'], 1):
                    channel_id = channel.get('channel_id', 'Unknown')
                    channel_name = channel.get('title', channel.get('username', 'Unknown'))
                    main_indicator = " (Main)" if channel.get('is_main_channel', False) else ""
                    message += f"{i}. {channel_id} - {channel_name}{main_indicator}\n"
            else:
                message += "No force subscribe channels configured."

            return message

        except Exception as e:
            logger.error(f"Error formatting admin channels message: {e}")
            return "⚙️ Hello, Welcome To The Channel Settings.\n\n🏷️ Total Channels In Check: 0\n\nNo force subscribe channels configured."

    async def add_channel_from_forwarded_message(
        self,
        channel_id: int,
        channel_title: str,
        channel_username: str,
        admin_user_id: int
    ) -> tuple:
        """Add channel from forwarded message with automatic verification"""
        try:
            # Validate input parameters
            if not channel_id or not channel_title:
                return False, (
                    "❌ <b>Invalid channel data</b>\n\n"
                    "The forwarded message doesn't contain valid channel information.\n"
                    "Please try forwarding a different message from the channel."
                )

            # Sanitize channel title and username
            channel_title = str(channel_title).strip()
            if channel_username:
                channel_username = str(channel_username).strip().lstrip('@')

            # Validate channel ID format
            if channel_id > 0:
                return False, (
                    "❌ <b>Invalid channel type</b>\n\n"
                    "This appears to be a private chat or group, not a channel.\n"
                    "Please forward a message from a channel (channels have negative IDs)."
                )
            # Verify bot has admin permissions in the channel
            try:
                chat_member = await self.bot.get_chat_member(channel_id, self.bot.id)
                if chat_member.status not in ['administrator', 'creator']:
                    bot_username = getattr(self.bot, 'username', 'Unknown')
                    return False, (
                        "❌ <b>Bot is not an administrator in this channel</b>\n\n"
                        "To add this channel:\n"
                        "1️⃣ Go to the channel settings\n"
                        "2️⃣ Add this bot as an administrator\n"
                        "3️⃣ Grant necessary permissions (at minimum: 'Post Messages')\n"
                        "4️⃣ Try forwarding a message again\n\n"
                        f"<b>Channel:</b> {channel_title}\n"
                        f"<b>Bot Username:</b> @{bot_username}\n"
                        f"<b>Current Status:</b> {chat_member.status.title()}"
                    )
            except TelegramError as e:
                logger.error(f"Error checking bot admin status in channel {channel_id}: {e}")
                error_details = ""
                if "chat not found" in str(e).lower():
                    error_details = "• The channel may be private and bot doesn't have access"
                elif "forbidden" in str(e).lower():
                    error_details = "• The bot doesn't have permission to access this channel"
                else:
                    error_details = f"• Error: {str(e)}"

                return False, (
                    "❌ <b>Cannot access channel</b>\n\n"
                    "Please ensure:\n"
                    "• The bot is added to the channel\n"
                    "• The bot has administrator permissions\n"
                    "• The channel is accessible\n\n"
                    f"<b>Channel:</b> {channel_title}\n"
                    f"<b>Details:</b> {error_details}"
                )

            # Check if channel already exists
            collection = await get_collection(COLLECTIONS['force_channels'])
            existing_channel = await collection.find_one({
                "$or": [
                    {"channel_id": channel_id},
                    {"username": channel_username} if channel_username else {"channel_id": channel_id}
                ]
            })

            if existing_channel:
                return False, (
                    "❌ <b>Channel already exists</b>\n\n"
                    "This channel is already in the force subscribe list.\n\n"
                    f"<b>Channel:</b> {channel_title}\n"
                    f"<b>Type:</b> {'Public' if channel_username else 'Private'}"
                )

            # Determine channel type and create channel data
            channel_type = "Public" if channel_username else "Private"

            channel_data = {
                'channel_id': channel_id,
                'title': channel_title,
                'username': channel_username,
                'type': 'force_sub',
                'is_main_channel': False,
                'added_by': admin_user_id,
                'added_at': get_current_timestamp(),
                'created_at': get_current_timestamp(),
                'updated_at': get_current_timestamp()
            }

            # Add channel to database
            result = await collection.insert_one(channel_data)

            if result.inserted_id:
                # Create success message with channel details
                success_message = (
                    "✅ <b>Channel Added Successfully!</b>\n\n"
                    f"📢 <b>Channel:</b> {channel_title}\n"
                    f"🆔 <b>ID:</b> {channel_id}\n"
                    f"📱 <b>Type:</b> {channel_type}\n"
                )

                if channel_username:
                    success_message += f"👤 <b>Username:</b> @{channel_username}\n"

                success_message += (
                    "🤖 <b>Bot Status:</b> Administrator ✅\n\n"
                    "The channel has been added to the force subscription list. "
                    "All new users will need to join this channel before using the bot."
                )

                return True, success_message
            else:
                return False, "❌ Failed to add channel to database. Please try again."

        except Exception as e:
            logger.error(f"Error adding channel from forwarded message: {e}")
            return False, "❌ Something went wrong while adding the channel. Please try again later."
