"""
Admin User Management service
Handles admin operations for user management with 100% PHP parity
"""

import logging
from typing import Optional, Dict, Any, List
from config.database import get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp
from telegram import Bot

logger = logging.getLogger(__name__)

class AdminUserService:
    """Service for admin user management operations"""
    
    def __init__(self):
        self.bot = Bot(settings.BOT_TOKEN)
        from services.user_service import UserService
        self.user_service = UserService()
    
    async def add_user_balance(self, user_id: int, amount: float, admin_id: int) -> Dict[str, Any]:
        """Add balance to user account (matching PHP logic exactly)"""
        try:
            # Get user data
            user = await self.user_service.get_user(user_id)
            if not user:
                return {
                    'success': False,
                    'message': f'User with ID {user_id} not found.'
                }
            
            # Get current balance
            current_balance = user.get('balance', 0)
            new_balance = current_balance + amount
            
            # Update user balance
            if await self.user_service.update_user_balance(user_id, amount, 'add'):
                # Log the transaction
                await self._log_admin_action(
                    admin_id, 
                    'add_balance', 
                    f"Added ₹{amount} to user {user_id} ({user.get('first_name', 'Unknown')}). New balance: ₹{new_balance}"
                )
                
                # Notify user
                await self._notify_user_balance_change(
                    user_id, 
                    amount, 
                    new_balance, 
                    'added'
                )
                
                return {
                    'success': True,
                    'message': f'Successfully added ₹{amount} to {user.get("first_name", "Unknown")}\'s account.\nNew balance: ₹{new_balance}',
                    'old_balance': current_balance,
                    'new_balance': new_balance
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to update user balance. Please try again.'
                }
            
        except Exception as e:
            logger.error(f"Error adding user balance: {e}")
            return {
                'success': False,
                'message': 'An error occurred while adding balance.'
            }
    
    async def remove_user_balance(self, user_id: int, amount: float, admin_id: int) -> Dict[str, Any]:
        """Remove balance from user account (matching PHP logic exactly)"""
        try:
            # Get user data
            user = await self.user_service.get_user(user_id)
            if not user:
                return {
                    'success': False,
                    'message': f'User with ID {user_id} not found.'
                }
            
            # Get current balance
            current_balance = user.get('balance', 0)
            
            # Check if user has sufficient balance
            if current_balance < amount:
                return {
                    'success': False,
                    'message': f'Insufficient balance. User has ₹{current_balance}, cannot remove ₹{amount}.'
                }
            
            new_balance = current_balance - amount
            
            # Update user balance
            if await self.user_service.update_user_balance(user_id, amount, 'subtract'):
                # Log the transaction
                await self._log_admin_action(
                    admin_id, 
                    'remove_balance', 
                    f"Removed ₹{amount} from user {user_id} ({user.get('first_name', 'Unknown')}). New balance: ₹{new_balance}"
                )
                
                # Notify user
                await self._notify_user_balance_change(
                    user_id, 
                    amount, 
                    new_balance, 
                    'removed'
                )
                
                return {
                    'success': True,
                    'message': f'Successfully removed ₹{amount} from {user.get("first_name", "Unknown")}\'s account.\nNew balance: ₹{new_balance}',
                    'old_balance': current_balance,
                    'new_balance': new_balance
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to update user balance. Please try again.'
                }
            
        except Exception as e:
            logger.error(f"Error removing user balance: {e}")
            return {
                'success': False,
                'message': 'An error occurred while removing balance.'
            }
    
    async def ban_user(self, user_id: int, admin_id: int, reason: str = None) -> Dict[str, Any]:
        """Ban user (matching PHP logic exactly)"""
        try:
            # Get user data
            user = await self.user_service.get_user(user_id)
            if not user:
                return {
                    'success': False,
                    'message': f'User with ID {user_id} not found.'
                }
            
            # Check if user is already banned
            if user.get('banned', False):
                return {
                    'success': False,
                    'message': f'{user.get("first_name", "Unknown")} is already banned.'
                }
            
            # Ban user
            collection = await get_collection(COLLECTIONS['users'])
            
            ban_data = {
                'banned': True,
                'banned_at': get_current_timestamp(),
                'banned_by': admin_id,
                'ban_reason': reason or 'No reason provided',
                'updated_at': get_current_timestamp()
            }
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": ban_data}
            )
            
            if result.modified_count > 0:
                # Log the action
                await self._log_admin_action(
                    admin_id, 
                    'ban_user', 
                    f"Banned user {user_id} ({user.get('first_name', 'Unknown')}). Reason: {reason or 'No reason provided'}"
                )
                
                # Notify user
                await self._notify_user_ban(user_id, reason)
                
                return {
                    'success': True,
                    'message': f'Successfully banned {user.get("first_name", "Unknown")}.',
                    'user_name': user.get('first_name', 'Unknown')
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to ban user. Please try again.'
                }
            
        except Exception as e:
            logger.error(f"Error banning user: {e}")
            return {
                'success': False,
                'message': 'An error occurred while banning user.'
            }
    
    async def unban_user(self, user_id: int, admin_id: int) -> Dict[str, Any]:
        """Unban user (matching PHP logic exactly)"""
        try:
            # Get user data
            user = await self.user_service.get_user(user_id)
            if not user:
                return {
                    'success': False,
                    'message': f'User with ID {user_id} not found.'
                }
            
            # Check if user is banned
            if not user.get('banned', False):
                return {
                    'success': False,
                    'message': f'{user.get("first_name", "Unknown")} is not banned.'
                }
            
            # Unban user
            collection = await get_collection(COLLECTIONS['users'])
            
            unban_data = {
                'banned': False,
                'unbanned_at': get_current_timestamp(),
                'unbanned_by': admin_id,
                'updated_at': get_current_timestamp()
            }
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": unban_data}
            )
            
            if result.modified_count > 0:
                # Log the action
                await self._log_admin_action(
                    admin_id, 
                    'unban_user', 
                    f"Unbanned user {user_id} ({user.get('first_name', 'Unknown')})"
                )
                
                # Notify user
                await self._notify_user_unban(user_id)
                
                return {
                    'success': True,
                    'message': f'Successfully unbanned {user.get("first_name", "Unknown")}.',
                    'user_name': user.get('first_name', 'Unknown')
                }
            else:
                return {
                    'success': False,
                    'message': 'Failed to unban user. Please try again.'
                }
            
        except Exception as e:
            logger.error(f"Error unbanning user: {e}")
            return {
                'success': False,
                'message': 'An error occurred while unbanning user.'
            }
    
    async def get_user_record(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive user record (matching PHP logic exactly)"""
        try:
            # Get user data
            user = await self.user_service.get_user(user_id)
            if not user:
                return {
                    'success': False,
                    'message': f'User with ID {user_id} not found.'
                }
            
            # Calculate statistics
            referral_count = len(user.get('promotion_report', []))
            withdrawal_reports = user.get('withdrawal_report', [])
            successful_withdrawals = [w for w in withdrawal_reports if w.get('status') == 'Passed']
            pending_withdrawals = [w for w in withdrawal_reports if w.get('status') == 'Pending']
            failed_withdrawals = [w for w in withdrawal_reports if w.get('status') == 'Failed']
            
            total_withdrawn = sum(w.get('amount', 0) for w in successful_withdrawals)
            pending_amount = sum(w.get('amount', 0) for w in pending_withdrawals)
            
            # Get task submissions
            from services.task_service import TaskService
            task_service = TaskService()
            user_submissions = await task_service.get_user_submissions(user_id)
            
            return {
                'success': True,
                'user': user,
                'statistics': {
                    'referral_count': referral_count,
                    'total_withdrawals': len(successful_withdrawals),
                    'total_withdrawn': total_withdrawn,
                    'pending_withdrawals': len(pending_withdrawals),
                    'pending_amount': pending_amount,
                    'failed_withdrawals': len(failed_withdrawals),
                    'task_submissions': len(user_submissions)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting user record: {e}")
            return {
                'success': False,
                'message': 'An error occurred while fetching user record.'
            }
    
    async def _log_admin_action(self, admin_id: int, action: str, details: str):
        """Log admin action for audit trail"""
        try:
            collection = await get_collection(COLLECTIONS['admin_logs'])
            
            log_entry = {
                'admin_id': admin_id,
                'action': action,
                'details': details,
                'timestamp': get_current_timestamp(),
                'date': get_current_timestamp()
            }
            
            await collection.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging admin action: {e}")
    
    async def _notify_user_balance_change(self, user_id: int, amount: float, new_balance: float, action: str):
        """Notify user of balance change with bank-style message format"""
        try:
            import random
            from datetime import datetime

            # Generate last 4 digits of user ID for account number
            last_4_digits = str(user_id)[-4:].zfill(4)

            # Generate random 10-digit message ID
            message_id = ''.join([str(random.randint(0, 9)) for _ in range(10)])

            # Get current timestamp in DD-MM-YYYY HH:MM:SS format
            current_time = datetime.now().strftime("%d-%m-%Y %H:%M:%S")

            # Format message based on action
            if action == 'added':
                message = f"Your A/c XXXXXXXX{last_4_digits} credited Rs.{amount} by INTP. Bal after txn Rs {new_balance}.00 Msg Id {message_id} Time {current_time} -TeamInstanto"
            else:
                message = f"Your A/c XXXXXXXX{last_4_digits} debited Rs.{amount} by INTP. Bal after txn Rs {new_balance}.00 Msg Id {message_id} Time {current_time} -TeamInstanto"

            try:
                await self.bot.send_message(user_id, message)
            except Exception:
                pass  # User may have blocked the bot

        except Exception as e:
            logger.error(f"Error notifying user of balance change: {e}")
    
    async def _notify_user_ban(self, user_id: int, reason: str):
        """Notify user of ban"""
        try:
            message = f"🚫 <b>Account Banned</b>\n\n"
            message += f"Your account has been banned by an administrator.\n\n"
            message += f"📝 <b>Reason:</b> {reason or 'No reason provided'}\n\n"
            message += f"For appeals, contact support."
            
            try:
                await self.bot.send_message(user_id, message, parse_mode='HTML')
            except Exception:
                pass  # User may have blocked the bot
            
        except Exception as e:
            logger.error(f"Error notifying user of ban: {e}")
    
    async def _notify_user_unban(self, user_id: int):
        """Notify user of unban"""
        try:
            message = f"✅ <b>Account Unbanned</b>\n\n"
            message += f"Your account has been unbanned by an administrator.\n\n"
            message += f"You can now use the bot normally."
            
            try:
                await self.bot.send_message(user_id, message, parse_mode='HTML')
            except Exception:
                pass  # User may have blocked the bot
            
        except Exception as e:
            logger.error(f"Error notifying user of unban: {e}")
