"""
Utility helper functions
Maintains identical functionality to PHP version
"""

import re
import time
import uuid
import asyncio
import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any, Union
from telegram import Bot
from telegram.error import TelegramError
from config.settings import settings
from utils.constants import RANK_EMOJIS, DEFAULT_RANK_EMOJI

logger = logging.getLogger(__name__)

# Simple cache for admin status to avoid repeated database calls
_admin_cache = {}
_cache_timeout = 60  # Cache for 60 seconds

def get_current_date() -> str:
    """Get current date in Indian timezone (matching PHP version)"""
    from zoneinfo import ZoneInfo
    tz = ZoneInfo(settings.TIMEZONE)
    return datetime.now(tz).strftime("%Y-%m-%d %H:%M:%S")

def get_current_timestamp() -> int:
    """Get current Unix timestamp"""
    return int(time.time())

def is_valid_telegram_id(user_id: Union[str, int]) -> bool:
    """Validate Telegram user ID (matching PHP version)"""
    try:
        user_id = int(user_id)
        return 1 <= user_id <= 9999999999  # Telegram ID range
    except (ValueError, TypeError):
        return False

def is_valid_custom_parameter(param: str) -> bool:
    """Validate custom referral parameter (matching PHP version)"""
    if not param or not isinstance(param, str):
        return False
    
    # Must be 3-30 characters, alphanumeric with hyphens/underscores
    # Cannot start or end with hyphens/underscores
    pattern = r'^[a-zA-Z0-9][a-zA-Z0-9_-]{1,28}[a-zA-Z0-9]$'
    return bool(re.match(pattern, param))

def is_valid_email(email: str) -> bool:
    """Validate email address"""
    if not email or not isinstance(email, str):
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def is_valid_mobile_number(mobile: str) -> bool:
    """Validate Indian mobile number (matching PHP version)"""
    if not mobile or not isinstance(mobile, str):
        return False
    
    # Remove any non-digit characters
    mobile = re.sub(r'\D', '', mobile)
    
    # Check for valid Indian mobile number patterns
    patterns = [
        r'^[6-9]\d{9}$',      # 10 digits starting with 6-9
        r'^91[6-9]\d{9}$',    # With country code 91
        r'^0[6-9]\d{9}$'      # With leading 0
    ]
    
    return any(re.match(pattern, mobile) for pattern in patterns)

def is_valid_ifsc_code(ifsc: str) -> bool:
    """Validate Indian IFSC code format"""
    if not ifsc or not isinstance(ifsc, str):
        return False

    # Remove any whitespace but keep original case for validation
    ifsc = ifsc.strip()

    # IFSC format: exactly 11 characters
    if len(ifsc) != 11:
        return False

    # IFSC format validation:
    # First 4 characters: uppercase letters only (A-Z)
    # 5th character: always 0 (zero)
    # Last 6 characters: uppercase letters and digits (A-Z, 0-9)
    pattern = r'^[A-Z]{4}0[A-Z0-9]{6}$'
    return bool(re.match(pattern, ifsc))

# Simple in-memory cache for IFSC details (to avoid repeated API calls)
_ifsc_cache = {}

async def fetch_bank_details_by_ifsc(ifsc: str) -> dict:
    """
    Fetch bank details using IFSC code from Razorpay API
    Returns dict with bank details or error information
    """
    import aiohttp
    import asyncio

    # First validate format with original case
    if not is_valid_ifsc_code(ifsc):
        return {
            'success': False,
            'error': 'invalid_format',
            'message': 'Invalid IFSC code format. Please enter a valid 11-character IFSC code.'
        }

    # Convert to uppercase for API call and caching
    ifsc = ifsc.upper().strip()

    # Check cache first
    if ifsc in _ifsc_cache:
        logger.info(f"Returning cached bank details for IFSC: {ifsc}")
        return _ifsc_cache[ifsc]

    api_url = f"https://ifsc.razorpay.com/{ifsc}"

    try:
        timeout = aiohttp.ClientTimeout(total=10)  # 10 second timeout

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(api_url) as response:
                if response.status == 200:
                    data = await response.json()

                    # Extract and format bank details
                    bank_details = {
                        'success': True,
                        'ifsc': ifsc,
                        'bank_name': data.get('BANK', 'Unknown Bank'),
                        'branch_name': data.get('BRANCH', 'Unknown Branch'),
                        'city': data.get('CITY', 'Unknown City'),
                        'state': data.get('STATE', 'Unknown State'),
                        'address': data.get('ADDRESS', 'Address not available'),
                        'contact': data.get('CONTACT', ''),
                        'rtgs': data.get('RTGS', False),
                        'neft': data.get('NEFT', False),
                        'imps': data.get('IMPS', False),
                        'upi': data.get('UPI', False)
                    }

                    # Cache successful results
                    _ifsc_cache[ifsc] = bank_details
                    logger.info(f"Cached bank details for IFSC: {ifsc}")

                    return bank_details

                elif response.status == 404:
                    return {
                        'success': False,
                        'error': 'ifsc_not_found',
                        'message': f'IFSC code "{ifsc}" not found. Please check and try again.'
                    }
                else:
                    return {
                        'success': False,
                        'error': 'api_error',
                        'message': f'Unable to fetch bank details. API returned status {response.status}.'
                    }

    except asyncio.TimeoutError:
        return {
            'success': False,
            'error': 'timeout',
            'message': 'Request timed out. Please check your internet connection and try again.'
        }
    except aiohttp.ClientError as e:
        return {
            'success': False,
            'error': 'connection_error',
            'message': 'Unable to connect to bank details service. Please try again later.'
        }
    except Exception as e:
        logger.error(f"Error fetching bank details for IFSC {ifsc}: {e}")
        return {
            'success': False,
            'error': 'unknown_error',
            'message': 'An unexpected error occurred. Please try again later.'
        }

def format_bank_details_message(bank_details: dict) -> str:
    """Format bank details into a professional Telegram message"""
    if not bank_details.get('success'):
        return f"❌ {bank_details.get('message', 'Unknown error')}"

    message = "✅ <b>Bank Details Found</b>\n"
    message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
    message += f"🏛️ <b>Bank Name:</b> {bank_details['bank_name']}\n"
    message += f"🏢 <b>Branch:</b> {bank_details['branch_name']}\n"
    message += f"🏙️ <b>City:</b> {bank_details['city']}\n"
    message += f"📍 <b>State:</b> {bank_details['state']}\n"
    message += f"🏛️ <b>IFSC Code:</b> {bank_details['ifsc']}\n\n"

    if bank_details.get('address') and bank_details['address'] != 'Address not available':
        message += f"📍 <b>Address:</b> {bank_details['address']}\n\n"

    # Add supported services
    services = []
    if bank_details.get('rtgs'): services.append('RTGS')
    if bank_details.get('neft'): services.append('NEFT')
    if bank_details.get('imps'): services.append('IMPS')
    if bank_details.get('upi'): services.append('UPI')

    if services:
        message += f"💳 <b>Supported Services:</b> {', '.join(services)}\n\n"

    message += "❓ <b>Are these bank details correct?</b>"

    return message

def is_valid_account_number(account_number: str) -> bool:
    """Validate bank account number"""
    if not account_number or not isinstance(account_number, str):
        return False
    
    # Remove any non-digit characters
    account_number = re.sub(r'\D', '', account_number)
    
    # Account number should be 9-18 digits
    return 9 <= len(account_number) <= 18

def is_valid_usdt_address(address: str) -> bool:
    """Validate USDT/Ethereum address"""
    if not address or not isinstance(address, str):
        return False
    
    # Ethereum address format: 0x followed by 40 hexadecimal characters
    pattern = r'^0x[a-fA-F0-9]{40}$'
    return bool(re.match(pattern, address))

def generate_unique_id(prefix: str = "") -> str:
    """Generate unique ID with optional prefix (matching PHP version)"""
    timestamp = str(int(time.time()))
    random_part = str(uuid.uuid4()).replace('-', '')[:8]
    
    if prefix:
        return f"{prefix}_{timestamp}.{random_part}"
    return f"{timestamp}.{random_part}"

def generate_task_id() -> str:
    """Generate task ID (matching PHP version format)"""
    return generate_unique_id("task")

def generate_broadcast_id() -> str:
    """Generate broadcast ID"""
    return generate_unique_id("broadcast")

def generate_submission_id() -> str:
    """Generate submission ID"""
    return generate_unique_id("submission")

def generate_withdrawal_id() -> str:
    """Generate withdrawal ID"""
    return generate_unique_id("withdrawal")

def get_rank_emoji(rank: int) -> str:
    """Get rank emoji for position (matching PHP version)"""
    return RANK_EMOJIS.get(rank, DEFAULT_RANK_EMOJI)

def format_currency(amount: Union[int, float]) -> str:
    """Format currency amount (Indian Rupees)"""
    return f"₹{amount:,.2f}".rstrip('0').rstrip('.')

def format_number(number: Union[int, float]) -> str:
    """Format number with commas"""
    return f"{number:,}"

def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to specified length"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def escape_html(text: str) -> str:
    """Escape HTML special characters"""
    if not text:
        return ""
    
    return (text
            .replace("&", "&amp;")
            .replace("<", "&lt;")
            .replace(">", "&gt;")
            .replace('"', "&quot;")
            .replace("'", "&#x27;"))

def clean_username(username: str) -> str:
    """Clean and format username"""
    if not username:
        return "No username"
    
    username = username.strip()
    if username.startswith('@'):
        return username
    return f"@{username}"

def get_display_name(user_data: Dict[str, Any]) -> str:
    """Get user display name (matching PHP version)"""
    first_name = user_data.get('first_name', '').strip()
    last_name = user_data.get('last_name', '').strip()
    
    if first_name and last_name:
        return f"{first_name} {last_name}"
    elif first_name:
        return first_name
    elif last_name:
        return last_name
    else:
        return "Unknown User"

async def get_welcome_message(joining_bonus: int, main_channel: str) -> str:
    """Generate welcome message with force subscription channels (matching PHP version exactly)"""
    # Use generic user-facing amount instead of actual configured amount (like PHP)
    user_display_amount = getattr(settings, 'USER_DISPLAY_BONUS_MAX', 100)

    # Get all force subscription channels
    try:
        from services.force_subscription_service import ForceSubscriptionService
        force_sub_service = ForceSubscriptionService()
        all_channels = await force_sub_service.get_force_subscription_channels()

        if not all_channels:
            # Fallback to main channel if no force sub channels (like PHP)
            message = f"🎁 Make Money Easily! Get upto ₹{user_display_amount}!\n\n"
            message += f"🔺 <a href=\"https://t.me/{main_channel}\">Click & Join Our Channel</a>\n\n"
            message += f"🔷 Must Join Our Channel Before Clicking On [𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔]"
            return message

        # Build multi-channel welcome message with custom format (like PHP)
        message = f"<b>🎁 Make Money Easily! Get Upto ₹{user_display_amount}!</b>\n\n"

        for channel in all_channels:
            channel_username = channel.get('username', '').replace('@', '')
            if not channel_username:
                # Use channel ID if no username
                channel_username = str(channel.get('channel_id', '')).replace('-100', '')

            message += f"🔺 <a href=\"https://t.me/{channel_username}\">Click & Join Our Channel</a>\n"

        message += f"\n<b>🔷 Must Join Above All Channels Before Clicking [𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔]</b>\n\n"
        return message

    except Exception as e:
        # Fallback to main channel on error
        message = f"🎁 Make Money Easily! Get upto ₹{user_display_amount}!\n\n"
        message += f"🔺 <a href=\"https://t.me/{main_channel}\">Click & Join Our Channel</a>\n\n"
        message += f"🔷 Must Join Our Channel Before Clicking On [𝙂𝙀𝙏 𝙈𝙊𝙉𝙀𝙔]"
        return message

def get_invitation_message(referral_link: str) -> str:
    """Generate invitation message (matching PHP version)"""
    return settings.INVITATION_MESSAGE_TEMPLATE.format(
        referral_link=referral_link,
        amount=settings.USER_DISPLAY_REFERRAL_MAX
    )

def get_referral_link(user_id: int, bot_username: str) -> str:
    """Generate referral link (matching PHP version)"""
    return f"https://t.me/{bot_username}?start={user_id}"

def get_custom_referral_link(custom_param: str, bot_username: str) -> str:
    """Generate custom referral link"""
    return f"https://t.me/{bot_username}?start={custom_param}"

async def send_safe_message(bot: Bot, chat_id: int, text: str, **kwargs) -> bool:
    """Send message with error handling"""
    try:
        await bot.send_message(chat_id=chat_id, text=text, **kwargs)
        return True
    except TelegramError as e:
        logger.error(f"Failed to send message to {chat_id}: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error sending message to {chat_id}: {e}")
        return False

async def edit_safe_message(bot: Bot, chat_id: int, message_id: int, text: str, **kwargs) -> bool:
    """Edit message with error handling"""
    try:
        await bot.edit_message_text(
            chat_id=chat_id, 
            message_id=message_id, 
            text=text, 
            **kwargs
        )
        return True
    except TelegramError as e:
        logger.error(f"Failed to edit message {message_id} in {chat_id}: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error editing message {message_id} in {chat_id}: {e}")
        return False

async def delete_safe_message(bot: Bot, chat_id: int, message_id: int) -> bool:
    """Delete message with error handling"""
    try:
        await bot.delete_message(chat_id=chat_id, message_id=message_id)
        return True
    except TelegramError as e:
        logger.error(f"Failed to delete message {message_id} in {chat_id}: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error deleting message {message_id} in {chat_id}: {e}")
        return False

def calculate_withdrawal_tax(amount: int, tax_config: Dict[str, Any]) -> int:
    """Calculate withdrawal tax based on configuration"""
    tax_type = tax_config.get('type', 'none')
    
    if tax_type == 'none':
        return 0
    elif tax_type == 'fixed':
        return tax_config.get('amount', 0)
    elif tax_type == 'percentage':
        percentage = tax_config.get('percentage', 0)
        return int((amount * percentage) / 100)
    
    return 0

def get_withdrawal_amounts_with_tax(tax_config: Dict[str, Any]) -> List[Dict[str, int]]:
    """Get withdrawal amounts with tax calculations"""
    amounts = []
    
    for amount in settings.WITHDRAWAL_AMOUNTS:
        tax = calculate_withdrawal_tax(amount, tax_config)
        net_amount = amount - tax
        
        amounts.append({
            'gross': amount,
            'tax': tax,
            'net': net_amount
        })
    
    return amounts

async def check_channel_membership(bot: Bot, user_id: int, channel_username: str) -> bool:
    """Check if user is member of a channel"""
    try:
        # Add @ if not present
        if not channel_username.startswith('@'):
            channel_username = f"@{channel_username}"
        
        member = await bot.get_chat_member(chat_id=channel_username, user_id=user_id)
        return member.status in ['member', 'administrator', 'creator']
    except TelegramError:
        return False
    except Exception as e:
        logger.error(f"Error checking channel membership: {e}")
        return False

def parse_start_parameter(text: str) -> Optional[str]:
    """Parse referral parameter from /start command"""
    if not text or not text.startswith('/start'):
        return None
    
    parts = text.split(' ', 1)
    if len(parts) < 2:
        return None
    
    return parts[1].strip()

def is_admin(user_id: int) -> bool:
    """Check if user is admin (includes dynamic admin list)"""
    logger.debug(f"Checking admin status for user {user_id}")

    # Check static admin IDs from settings first
    if user_id in settings.ADMIN_IDS:
        logger.debug(f"User {user_id} found in static admin list")
        return True

    # Check dynamic admin list from database
    try:
        import asyncio
        from services.admin_service import AdminService

        # Handle event loop more carefully
        try:
            # Try to get the current running loop
            loop = asyncio.get_running_loop()
            logger.debug(f"Found running event loop for user {user_id}")

            # We're in an async context, but this is a sync function
            # This is problematic - we should use the async version instead
            logger.warning(f"Sync is_admin() called from async context for user {user_id} - consider using is_admin_async()")

            # Create a new task to run the async function
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, _check_admin_in_new_loop(user_id))
                result = future.result(timeout=5)  # 5 second timeout
                return result

        except RuntimeError:
            # No running loop, we can safely create one
            logger.debug(f"No running loop, creating new one for user {user_id}")
            return asyncio.run(_check_admin_in_new_loop(user_id))

    except Exception as e:
        logger.error(f"Error checking dynamic admin list for user {user_id}: {e}")
        # Fallback to static admin list only
        return False

async def _check_admin_in_new_loop(user_id: int) -> bool:
    """Helper function to check admin status in a new event loop"""
    try:
        from services.admin_service import AdminService

        admin_service = AdminService()
        # Always check admin list from PRIMARY admin's settings
        admin_settings = await admin_service.get_admin_settings(settings.ADMIN_ID)
        admin_list = admin_settings.get('admin_list', [])

        logger.debug(f"Retrieved admin list from database: {admin_list}")

        is_in_admin_list = user_id in admin_list
        logger.debug(f"User {user_id} {'found' if is_in_admin_list else 'not found'} in dynamic admin list")

        return is_in_admin_list

    except Exception as e:
        logger.error(f"Error in _check_admin_in_new_loop for user {user_id}: {e}")
        return False

async def is_admin_async(user_id: int) -> bool:
    """Async version of admin check - preferred for use in async contexts"""
    logger.debug(f"Async checking admin status for user {user_id}")

    # Check static admin IDs from settings first
    if user_id in settings.ADMIN_IDS:
        logger.debug(f"User {user_id} found in static admin list")
        return True

    # Check cache first
    current_time = time.time()
    cache_key = f"admin_{user_id}"

    if cache_key in _admin_cache:
        cached_result, cached_time = _admin_cache[cache_key]
        if current_time - cached_time < _cache_timeout:
            logger.debug(f"User {user_id} admin status from cache: {cached_result}")
            return cached_result
        else:
            # Cache expired, remove it
            del _admin_cache[cache_key]

    # Check dynamic admin list from database
    try:
        from services.admin_service import AdminService

        admin_service = AdminService()
        # Always check admin list from PRIMARY admin's settings
        admin_settings = await admin_service.get_admin_settings(settings.ADMIN_ID)
        admin_list = admin_settings.get('admin_list', [])

        logger.debug(f"Retrieved admin list from database: {admin_list}")

        is_in_admin_list = user_id in admin_list
        logger.debug(f"User {user_id} {'found' if is_in_admin_list else 'not found'} in dynamic admin list")

        # Cache the result
        _admin_cache[cache_key] = (is_in_admin_list, current_time)

        return is_in_admin_list

    except Exception as e:
        logger.error(f"Error checking dynamic admin list for user {user_id}: {e}")
        # Fallback to static admin list only
        return user_id in settings.ADMIN_IDS

def clear_admin_cache(user_id: int = None) -> None:
    """Clear admin cache for a specific user or all users"""
    global _admin_cache

    if user_id is not None:
        cache_key = f"admin_{user_id}"
        if cache_key in _admin_cache:
            del _admin_cache[cache_key]
            logger.debug(f"Cleared admin cache for user {user_id}")
    else:
        _admin_cache.clear()
        logger.debug("Cleared all admin cache")

def get_all_admin_ids() -> List[int]:
    """Get all admin IDs"""
    return settings.ADMIN_IDS.copy()

async def sleep_with_jitter(base_delay: float, max_jitter: float = 0.1) -> None:
    """Sleep with random jitter to avoid thundering herd"""
    import random
    jitter = random.uniform(0, max_jitter)
    await asyncio.sleep(base_delay + jitter)

def is_valid_email(email: str) -> bool:
    """Validate email format"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def is_valid_mobile_number(mobile: str) -> bool:
    """Validate mobile number format - requires international format with + prefix"""
    import re
    if not mobile or not isinstance(mobile, str):
        return False

    # Require international format with + prefix
    # Format: +[country_code][number] where total length is 10-15 digits after +
    pattern = r'^\+[1-9]\d{9,14}$'
    return bool(re.match(pattern, mobile))



def is_valid_account_number(account: str) -> bool:
    """Validate account number format"""
    # Basic validation: 9-18 digits
    return account.isdigit() and 9 <= len(account) <= 18

def generate_broadcast_id() -> str:
    """Generate unique broadcast ID"""
    import uuid
    return f"broadcast_{uuid.uuid4().hex[:12]}"
