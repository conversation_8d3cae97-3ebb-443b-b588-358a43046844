"""
Session handlers for multi-step operations
Maintains identical functionality to PHP version
"""

import logging
from typing import Optional, Dict, Any, List
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes

from config.database import get_collection, COLLECTIONS
from utils.helpers import get_current_timestamp
from utils.constants import PROCESS_CANCELLED

logger = logging.getLogger(__name__)

class SessionHandlers:
    """Handles user sessions for multi-step operations"""
    
    async def get_user_session(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user session"""
        try:
            collection = await get_collection(COLLECTIONS['sessions'])
            session = await collection.find_one({"user_id": user_id})
            return session
        except Exception as e:
            logger.error(f"Error getting user session {user_id}: {e}")
            return None
    
    async def set_user_session(self, user_id: int, step: str, data: Dict[str, Any] = None) -> bool:
        """Set user session"""
        try:
            collection = await get_collection(COLLECTIONS['sessions'])

            session_data = {
                "user_id": user_id,
                "step": step,
                "data": data or {},
                "created_at": get_current_timestamp(),
                "updated_at": get_current_timestamp()
            }

            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": session_data},
                upsert=True
            )

            return result.modified_count > 0 or result.upserted_id is not None

        except Exception as e:
            logger.error(f"Error setting user session {user_id}: {e}")
            return False

    async def update_user_session_data(self, user_id: int, step: str, new_data: Dict[str, Any]) -> bool:
        """Update user session data by merging with existing data"""
        try:
            collection = await get_collection(COLLECTIONS['sessions'])

            # Get existing session
            existing_session = await collection.find_one({"user_id": user_id})

            if existing_session:
                # Merge existing data with new data
                existing_data = existing_session.get('data', {})
                merged_data = {**existing_data, **new_data}

                # Update with merged data
                result = await collection.update_one(
                    {"user_id": user_id},
                    {
                        "$set": {
                            "step": step,
                            "data": merged_data,
                            "updated_at": get_current_timestamp()
                        }
                    }
                )
            else:
                # Create new session if none exists
                session_data = {
                    "user_id": user_id,
                    "step": step,
                    "data": new_data,
                    "created_at": get_current_timestamp(),
                    "updated_at": get_current_timestamp()
                }

                result = await collection.insert_one(session_data)
                return result.inserted_id is not None

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating user session data {user_id}: {e}")
            return False

    async def get_link_redeem_config(self, user_id: int) -> Dict[str, Any]:
        """Get link redeem configuration data from session"""
        try:
            session = await self.get_user_session(user_id)
            if session and isinstance(session, dict) and session.get('step') == 'link_redeem_config':
                return session.get('data', {})
            return {}
        except Exception as e:
            logger.error(f"Error getting link redeem config for user {user_id}: {e}")
            return {}
    
    async def clear_user_session(self, user_id: int) -> bool:
        """Clear user session"""
        try:
            collection = await get_collection(COLLECTIONS['sessions'])
            result = await collection.delete_one({"user_id": user_id})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"Error clearing user session {user_id}: {e}")
            return False
    
    async def update_session_data(self, user_id: int, data: Dict[str, Any]) -> bool:
        """Update session data"""
        try:
            collection = await get_collection(COLLECTIONS['sessions'])
            
            result = await collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "data": data,
                    "updated_at": get_current_timestamp()
                }}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Error updating session data {user_id}: {e}")
            return False
    
    async def handle_cancel_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /cancel command"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id

        try:
            # First check if user has an active broadcast (for admins)
            from utils.helpers import is_admin
            if is_admin(user_id):
                from services.admin_service import AdminService
                admin_service = AdminService()

                active_broadcast = await admin_service.get_active_broadcast(user_id)
                if active_broadcast:
                    # Cancel the active broadcast
                    broadcast_id = active_broadcast['broadcast_id']
                    success = await admin_service.cancel_broadcast(broadcast_id, user_id)

                    if success:
                        await update.message.reply_text(
                            f"🚫 <b>Broadcast Cancelled</b>\n\n"
                            f"Broadcast ID: <code>{broadcast_id[-8:]}</code>\n\n"
                            f"The broadcast has been stopped and will complete processing current users.",
                            parse_mode='HTML'
                        )
                        return
                    else:
                        await update.message.reply_text(
                            "❌ Failed to cancel broadcast. Please try again.",
                            parse_mode='HTML'
                        )
                        return

            # Check for regular user session
            session = await self.get_user_session(user_id)

            if session:
                step = session.get('step', '')

                # Provide specific cancellation messages based on session type
                if 'gift_code' in step:
                    cancel_message = "🚫 <b>Gift Code Process Cancelled</b>\n\nYou can try claiming a gift code again anytime."
                elif 'broadcast' in step:
                    cancel_message = "🚫 <b>Broadcast Process Cancelled</b>\n\nYour broadcast has been cancelled."
                elif 'balance' in step:
                    cancel_message = "🚫 <b>Balance Operation Cancelled</b>\n\nNo changes have been made."
                elif 'withdrawal' in step:
                    cancel_message = "🚫 <b>Withdrawal Process Cancelled</b>\n\nYour withdrawal request has been cancelled."
                elif 'force_channel' in step:
                    cancel_message = "🚫 <b>Channel Operation Cancelled</b>\n\nNo changes have been made to force subscription channels."
                else:
                    cancel_message = PROCESS_CANCELLED

                # Clear the session
                await self.clear_user_session(user_id)
                await update.message.reply_text(cancel_message, parse_mode='HTML')
            else:
                # No active session
                await update.message.reply_text(
                    "ℹ️ <b>No Active Process</b>\n\nThere's no active process to cancel.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in handle_cancel_command: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
    
    async def handle_session_message(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE, 
        session: Dict[str, Any]
    ) -> None:
        """Handle message based on session step"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        step = session.get('step')
        data = session.get('data', {})

        # Debug logging
        logger.info(f"DEBUG: handle_session_message called for user {user_id}, step: {step}")
        
        try:
            # Handle cancel command in session
            if update.message and update.message.text == '/cancel':
                await self.handle_cancel_command(update, context)
                return
            
            # Route to appropriate handler based on step
            if step == 'add_balance_id':
                await self._handle_add_balance_step2(update, context)
            elif step == 'add_balance_amount':
                await self._handle_add_balance_step3(update, context, data)
            elif step == 'remove_balance_id':
                await self._handle_remove_balance_step2(update, context)
            elif step == 'remove_balance_amount':
                await self._handle_remove_balance_step3(update, context, data)
            elif step == 'ban_user_id':
                await self._handle_ban_user_step2(update, context)
            elif step == 'unban_user_id':
                await self._handle_unban_user_step2(update, context)
            # Note: set_main_channel routing moved to FORCESUB section
            elif step == 'set_private_logs_channel':
                await self._handle_set_private_logs_channel_step2(update, context)
            elif step == 'set_maintenance_status':
                await self._handle_set_maintenance_status_step2(update, context)
            elif step == 'set_otp_api_key':
                await self._handle_set_otp_api_key_step2(update, context)
            elif step == 'set_per_refer_amount':
                await self._handle_set_per_refer_amount_step2(update, context)
            elif step == 'set_joining_bonus_amount':
                await self._handle_set_joining_bonus_amount_step2(update, context)

            # ==================== NEW ENHANCED ADMIN PANEL SESSION HANDLERS ====================
            elif step == 'set_percent_tax':
                await self._handle_set_percent_tax_step2(update, context)
            elif step == 'set_fixed_tax':
                await self._handle_set_fixed_tax_step2(update, context)
            elif step == 'ban_user_withdrawal':
                await self._handle_ban_user_withdrawal_step2(update, context)
            elif step == 'change_user_cashout':
                await self._handle_change_user_cashout_step2(update, context)

            elif step == 'check_user_record':
                await self._handle_check_user_record_step2(update, context)
            elif step == 'pass_user_withdrawal':
                await self._handle_pass_user_withdrawal_step2(update, context)
            elif step == 'fail_user_withdrawal':
                await self._handle_fail_user_withdrawal_step2(update, context)
            elif step == 'broadcast_gift_channel':
                await self._handle_broadcast_gift_step2(update, context)
            elif step == 'broadcast_gift_invite_link':
                await self._handle_broadcast_gift_invite_link_step(update, context, data)
            elif step == 'broadcast_gift_amount':
                await self._handle_broadcast_gift_step3(update, context, data)

            elif step == 'broadcast_media':
                await self._handle_broadcast_media_step2(update, context, data)
            elif step == 'broadcast_text':
                await self._handle_broadcast_text_step2(update, context, data)
            elif step == 'broadcast_buttons':
                await self._handle_broadcast_buttons_step2(update, context, data)
            # Note: broadcast_message handler removed - now using separate media/text/buttons handlers
            elif step == 'set_account_name':
                await self._handle_set_account_name_step2(update, context)
            elif step == 'set_account_ifsc':
                await self._handle_set_account_ifsc_step2(update, context)
            elif step == 'set_account_ifsc_manual':
                await self._handle_set_account_ifsc_manual_step2(update, context)
            elif step == 'set_account_email':
                await self._handle_set_account_email_step2(update, context)
            elif step == 'set_account_number':
                await self._handle_set_account_number_step2(update, context)
            elif step == 'set_mobile_number':
                await self._handle_set_mobile_number_step2(update, context)
            elif step == 'verify_otp':
                await self._handle_verify_otp_step2(update, context, data)
            elif step == 'add_force_sub_channel':
                await self._handle_add_force_sub_channel_step2(update, context)
            elif step == 'add_force_channel_forward':
                await self._handle_add_force_channel_forward_step2(update, context)
            elif step == 'view_user_details':
                await self._handle_view_user_details_step2(update, context)
            elif step == 'reset_user_method':
                await self._handle_reset_user_method_step2(update, context)
            elif step == 'submit_task_screenshot':
                await self._handle_task_screenshot_submission(update, context, data)

            # ==================== NEW USER BONUS MANAGEMENT SESSION HANDLERS ====================
            elif step == 'set_new_user_bonus':
                await self._handle_set_new_user_bonus_step2(update, context)
            elif step == 'set_invite_bonus':
                await self._handle_set_invite_bonus_step2(update, context)
            elif step == 'configure_level_rewards':
                await self._handle_configure_level_rewards_step2(update, context)
            elif step == 'configure_level_rewards_amounts':
                await self._handle_configure_level_rewards_step3(update, context, data)

            # ==================== NEW USER DETAILS & SETTINGS SESSION HANDLERS ====================
            elif step == 'user_details_get_id':
                await self._handle_user_details_get_id_step2(update, context)
            elif step == 'add_user_balance':
                await self._handle_add_user_balance_step2(update, context, data)
            elif step == 'remove_user_balance':
                await self._handle_remove_user_balance_step2(update, context, data)
            elif step == 'send_user_message':
                await self._handle_send_user_message_step2(update, context, data)
            elif step == 'reject_task_submission':
                await self._handle_reject_task_submission_step2(update, context, data)

            # ==================== TASK MANAGEMENT SESSION HANDLERS ====================
            elif step == 'edit_task_name':
                await self._handle_edit_task_name_step2(update, context, data)
            elif step == 'edit_task_bonus':
                await self._handle_edit_task_bonus_step2(update, context, data)
            elif step == 'edit_task_content':
                await self._handle_edit_task_content_step2(update, context, data)
            elif step == 'edit_task_image':
                await self._handle_edit_task_image_step2(update, context, data)
            elif step == 'add_task_step1':
                await self._handle_add_task_step2(update, context)
            elif step == 'add_task_step2':
                await self._handle_add_task_step3(update, context, data)
            elif step == 'add_task_step3':
                await self._handle_add_task_step4(update, context, data)

            # ==================== ADMIN MANAGEMENT SESSION HANDLERS ====================
            elif step == 'add_admin':
                await self._handle_add_admin_step2(update, context)
            elif step == 'remove_admin':
                await self._handle_remove_admin_step2(update, context)

            # ==================== BROADCAST SESSION HANDLERS ====================
            elif step == 'broadcast_media':
                await self._handle_broadcast_media_step2(update, context)
            elif step == 'broadcast_text':
                await self._handle_broadcast_text_step2(update, context)
            elif step == 'broadcast_buttons':
                await self._handle_broadcast_buttons_step2(update, context)

            # ==================== GIFT CODE SESSION HANDLERS ====================
            elif step == 'add_redeem_code':
                await self._handle_add_redeem_code_step2(update, context)
            elif step == 'link_fixed_amount':
                await self._handle_link_fixed_amount_step2(update, context)
            elif step == 'link_random_amount':
                await self._handle_link_random_amount_step2(update, context)
            elif step == 'link_user_limit':
                await self._handle_link_user_limit_step2(update, context)

            # ==================== NEW STREAMLINED LINK REDEEM WORKFLOW ====================
            elif step == 'link_fixed_amount_step1':
                await self._handle_link_fixed_amount_new_step1(update, context)
            elif step == 'link_fixed_amount_step2':
                await self._handle_link_fixed_amount_new_step2(update, context)
            elif step == 'link_random_amount_step1':
                await self._handle_link_random_amount_new_step1(update, context)
            elif step == 'link_random_amount_step2':
                await self._handle_link_random_amount_new_step2(update, context)

            # ==================== FORCESUB SESSION HANDLERS ====================
            elif step == 'set_main_channel':
                await self._handle_set_main_channel_step2(update, context)
            elif step == 'add_forcesub_channel':
                await self._handle_add_forcesub_channel_step2(update, context)
            elif step == 'remove_forcesub_channel':
                await self._handle_remove_forcesub_channel_step2(update, context)

            # ==================== GIFT BROADCAST SESSION HANDLERS ====================
            elif step == 'gift_broadcast_forward':
                await self._handle_gift_broadcast_forward_step2(update, context)
            elif step == 'gift_broadcast_invite_link':
                await self._handle_gift_broadcast_invite_link_step2(update, context)
            elif step == 'gift_broadcast_reward':
                await self._handle_gift_broadcast_reward_step2(update, context)
            # Note: gift_broadcast_channel routing moved to NEW GIFT BROADCAST section

            # ==================== MAINTENANCE & CUSTOM REFERRAL SESSION HANDLERS ====================
            elif step == 'delete_invite_link':
                await self._handle_delete_invite_link_step2(update, context)
            elif step == 'create_custom_referral':
                await self._handle_create_custom_referral_step2(update, context)
            elif step == 'redeem_gift_code':
                await self._handle_redeem_gift_code_step2(update, context)
            elif step == 'add_task_name':
                await self._handle_add_task_step2(update, context)
            elif step == 'add_task_description':
                await self._handle_add_task_step3(update, context, data)
            elif step == 'add_task_reward':
                await self._handle_add_task_step4(update, context, data)
            elif step == 'add_task_media':
                await self._handle_add_task_step5(update, context, data)
            elif step == 'edit_task_name':
                await self._handle_edit_task_name_step2(update, context)
            elif step == 'edit_task_description':
                await self._handle_edit_task_description_step2(update, context)
            elif step == 'edit_task_reward':
                await self._handle_edit_task_reward_step2(update, context)
            elif step == 'edit_task_media':
                await self._handle_edit_task_media_step2(update, context)
            elif step == 'reset_user_account_id':
                await self._handle_reset_user_account_step2(update, context)
            elif step == 'set_account_name':
                await self._handle_set_account_name_step2(update, context)
            elif step == 'set_account_ifsc':
                await self._handle_set_account_ifsc_step2(update, context)
            elif step == 'set_account_ifsc_manual':
                await self._handle_set_account_ifsc_manual_step2(update, context)
            elif step == 'set_account_email':
                await self._handle_set_account_email_step2(update, context)
            elif step == 'set_account_number':
                await self._handle_set_account_number_step2(update, context)
            elif step == 'set_mobile_number':
                await self._handle_set_mobile_number_step2(update, context)
            elif step == 'verify_otp':
                await self._handle_verify_otp_step2(update, context, data)
            elif step == 'set_binance_id':
                await self._handle_set_binance_id_step2(update, context)
            elif step == 'gift_broadcast_channel':
                await self._handle_gift_broadcast_channel_step2(update, context)
            elif step == 'gift_broadcast_amount':
                await self._handle_gift_broadcast_amount_step2(update, context, data)
            elif step == 'configure_level_settings':
                await self._handle_configure_level_settings_step2(update, context)
            elif step == 'generate_gift_code':
                await self._handle_generate_gift_code_step2(update, context)
            elif step == 'generate_gift_amount':
                await self._handle_generate_gift_amount_step2(update, context, data)
            elif step == 'generate_gift_limit':
                await self._handle_generate_gift_limit_step2(update, context, data)

            elif step == 'add_balance_user_id':
                await self._handle_add_balance_user_id_step2(update, context)
            elif step == 'add_balance_amount':
                await self._handle_add_balance_amount_step2(update, context, data)
            elif step == 'remove_balance_user_id':
                await self._handle_remove_balance_user_id_step2(update, context)
            elif step == 'remove_balance_amount':
                await self._handle_remove_balance_amount_step2(update, context, data)
            elif step == 'ban_user_id':
                await self._handle_ban_user_id_step2(update, context)
            elif step == 'ban_user_reason':
                await self._handle_ban_user_reason_step2(update, context, data)
            elif step == 'unban_user_id':
                await self._handle_unban_user_id_step2(update, context)
            elif step == 'check_user_record':
                await self._handle_check_user_record_step2(update, context)
            elif step == 'add_force_channel':
                await self._handle_add_force_channel_step2(update, context)
            elif step == 'remove_force_channel':
                await self._handle_remove_force_channel_step2(update, context)
            elif step == 'generate_gift_code':
                await self._handle_generate_gift_code_step2(update, context)
            elif step == 'generate_gift_amount':
                await self._handle_generate_gift_code_step3(update, context, data)
            elif step == 'generate_gift_limit':
                await self._handle_generate_gift_code_step4(update, context, data)
            elif step == 'configure_level_referrals':
                await self._handle_configure_level_referrals_step2(update, context)
            elif step == 'configure_level_bonuses':
                await self._handle_configure_level_bonuses_step3(update, context, data)
            elif step == 'set_withdrawal_tax':
                await self._handle_set_withdrawal_tax_step2(update, context)
            elif step in ['set_usdt_address', 'set_binance_id']:
                await self._handle_set_binance_id_step2(update, context)
            else:
                # Unknown step, clear session
                await self.clear_user_session(user_id)
                
        except Exception as e:
            logger.error(f"Error handling session message for step {step}: {e}")
            await self.clear_user_session(user_id)
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
    
    # Placeholder methods for session handlers
    # These will be implemented in their respective service modules
    
    async def _handle_add_balance_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add balance step 2 - get user ID"""
        from handlers.admin_handlers import AdminHandlers
        admin_handlers = AdminHandlers()
        await admin_handlers.handle_add_balance_step2(update, context)
    
    async def _handle_add_balance_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle add balance step 3 - get amount"""
        from handlers.admin_handlers import AdminHandlers
        admin_handlers = AdminHandlers()
        await admin_handlers.handle_add_balance_step3(update, context, data)
    
    async def _handle_remove_balance_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove balance step 2 - get user ID"""
        from handlers.admin_handlers import AdminHandlers
        admin_handlers = AdminHandlers()
        await admin_handlers.handle_remove_balance_step2(update, context)
    
    async def _handle_remove_balance_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle remove balance step 3 - get amount"""
        from handlers.admin_handlers import AdminHandlers
        admin_handlers = AdminHandlers()
        await admin_handlers.handle_remove_balance_step3(update, context, data)
    
    async def _handle_ban_user_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle ban user step 2"""
        from handlers.admin_handlers import AdminHandlers
        admin_handlers = AdminHandlers()
        await admin_handlers.handle_ban_user_step2(update, context)
    
    async def _handle_unban_user_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle unban user step 2"""
        from handlers.admin_handlers import AdminHandlers
        admin_handlers = AdminHandlers()
        await admin_handlers.handle_unban_user_step2(update, context)
    
    # Additional placeholder methods for other session steps
    # These will be implemented as we build out the respective handlers
    
    async def _handle_set_account_name_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set account name step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            # Validate name
            if len(text) < 2:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set name')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Name is too short. Please enter at least 2 characters.",
                    reply_markup=keyboard
                )
                return

            if len(text) > 50:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set name')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Name is too long. Please keep it under 50 characters.",
                    reply_markup=keyboard
                )
                return

            # Update account info
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            if await withdrawal_service.update_account_info(user_id, 'name', text):
                # Clear session
                await self.clear_user_session(user_id)

                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📝 Continue Setup', callback_data='setAccountInfo')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                success_message = f"✅ <b>Name Updated</b>\n\n"
                success_message += f"👤 <b>Full Name:</b> {text}\n\n"
                success_message += "Your account name has been updated successfully."

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text("❌ Failed to update name. Please try again.")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_account_name_step2: {e}")
            await update.message.reply_text("❌ Error updating name. Please try again.")
            await self.clear_user_session(user_id)
    

    
    async def _handle_task_screenshot_submission(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle task screenshot submission (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        message = update.message

        try:
            # Check maintenance and ban status
            from services.user_service import UserService
            user_service = UserService()

            if await user_service.is_maintenance_mode():
                await update.message.reply_text(
                    "⚙️ Bot is currently under maintenance, please try again later.",
                    parse_mode='HTML'
                )
                await self.clear_user_session(user_id)
                return

            user = await user_service.get_user(user_id)
            if user and user.get('banned', False):
                await update.message.reply_text(
                    "🚫 You are banned from using this bot.",
                    parse_mode='HTML'
                )
                await self.clear_user_session(user_id)
                return

            task_id = data.get('task_id')
            if not task_id:
                await update.message.reply_text("❌ Invalid task. Please try again.")
                await self.clear_user_session(user_id)
                return

            from services.task_service import TaskService
            task_service = TaskService()

            task = await task_service.get_task_by_id(task_id)
            if not task or task['status'] != 'active':
                await update.message.reply_text("❌ Task not found or no longer available.")
                await self.clear_user_session(user_id)
                return

            # Check if user has already submitted this task
            user_submission = await task_service.check_user_task_submission(user_id, task_id)
            if user_submission:
                await update.message.reply_text("❌ You have already submitted this task.")
                await self.clear_user_session(user_id)
                return

            # Get file_id from photo or document
            file_id = None

            if message.photo:
                # Get the highest resolution photo
                file_id = message.photo[-1].file_id
            elif message.document:
                file_id = message.document.file_id
            else:
                await update.message.reply_text(
                    "❌ Please send a photo or document as proof.\n\nSend /cancel to cancel the submission."
                )
                return

            # Create submission
            from models.task import TaskSubmissionModel

            submission_data = TaskSubmissionModel.create_new_submission(
                user_id=user_id,
                task_id=task_id,
                file_id=file_id,
                status='pending'
            )

            submission_id = await task_service.add_task_submission(submission_data)

            if submission_id:
                # Clear session
                await self.clear_user_session(user_id)

                # Notify user
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📋 View Tasks', callback_data='taskRewards')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                success_message = f"""✅ <b>Task Submitted Successfully!</b>

📋 <b>Task:</b> {task['name']}
💰 <b>Reward:</b> ₹{task['reward_amount']}

⏳ Your submission is under review. You will be notified once it's approved."""

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                # Notify all admins
                await task_service.notify_admins_new_submission(
                    submission_data, task, user, file_id
                )

            else:
                await update.message.reply_text("❌ Error submitting task. Please try again.")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_task_screenshot_submission: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
            await self.clear_user_session(user_id)
    
    # Add more placeholder methods as needed for other session steps
    async def _handle_set_main_channel_step2_old(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set main channel step 2 (old version - deprecated)"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            # Use the new force subscription service instead
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService()

            success, message = await force_service.set_main_channel(text, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            # Show result with navigation buttons
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            if success:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏡 Set Another Channel', callback_data='set_main_channel')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='forcesub_channels')]
                ])
            else:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set_main_channel')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='forcesub_channels')]
                ])

            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Update main channel setting
            success = await admin_service.update_admin_setting(user_id, 'main_channel', text)

            if success:
                from config.settings import settings
                bot_username = settings.BOT_USERNAME

                message = f"✅ <b>Main Channel Updated</b>\n\n"
                message += f"📢 <b>Main channel set to:</b> @{text}\n\n"
                message += f"⚠️ <b>Note:</b> Must make @{bot_username} admin in the channel."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏠 Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Error setting main channel.", parse_mode='HTML')

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_main_channel_step2: {e}")
            await update.message.reply_text("❌ Something went wrong. Please try again later.", parse_mode='HTML')
            await self.clear_user_session(user_id)

    async def _handle_set_private_logs_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set private logs channel step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Update private logs channel setting
            success = await admin_service.update_admin_setting(user_id, 'private_logs_channel', text)

            if success:
                message = f"✅ <b>Private Logs Channel Updated</b>\n\n"
                message += f"📋 <b>Private logs channel set to:</b> {text}"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏠 Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Error setting private logs channel.", parse_mode='HTML')

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_private_logs_channel_step2: {e}")
            await update.message.reply_text("❌ Something went wrong. Please try again later.", parse_mode='HTML')
            await self.clear_user_session(user_id)

    async def _handle_set_maintenance_status_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set maintenance status step 2 (matching PHP version exactly)"""
        # This is handled directly in the callback, no step 2 needed
        pass

    async def _handle_set_otp_api_key_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set OTP API key step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Update OTP API key setting
            success = await admin_service.update_admin_setting(user_id, 'otp_website_api_key', text)

            if success:
                message = f"✅ <b>OTP API Key Updated</b>\n\n"
                message += f"🔑 <b>OTP website API key set to:</b> {text}"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏠 Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Error setting OTP API key.", parse_mode='HTML')

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_otp_api_key_step2: {e}")
            await update.message.reply_text("❌ Something went wrong. Please try again later.", parse_mode='HTML')
            await self.clear_user_session(user_id)

    async def _handle_set_per_refer_amount_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set per refer amount step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            # Validate format (min-max)
            if '-' not in text:
                await update.message.reply_text("❌ Invalid format. Use format: min-max (e.g., 20-50)", parse_mode='HTML')
                await self.clear_user_session(user_id)
                return

            try:
                min_amount, max_amount = text.split('-')
                min_amount = int(min_amount.strip())
                max_amount = int(max_amount.strip())

                if min_amount <= 0 or max_amount <= 0 or min_amount > max_amount:
                    raise ValueError("Invalid range")

            except ValueError:
                await update.message.reply_text("❌ Invalid range. Please enter valid numbers.", parse_mode='HTML')
                await self.clear_user_session(user_id)
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Update per refer amount setting
            success = await admin_service.update_admin_setting(user_id, 'per_refer_amount_range', text)

            if success:
                message = f"✅ <b>Per Refer Amount Updated</b>\n\n"
                message += f"💰 <b>Per refer amount range set to:</b> ₹{text}"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏠 Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Error setting per refer amount.", parse_mode='HTML')

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_per_refer_amount_step2: {e}")
            await update.message.reply_text("❌ Something went wrong. Please try again later.", parse_mode='HTML')
            await self.clear_user_session(user_id)

    async def _handle_set_joining_bonus_amount_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set joining bonus amount step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            # Validate format (min-max)
            if '-' not in text:
                await update.message.reply_text("❌ Invalid format. Use format: min-max (e.g., 20-50)", parse_mode='HTML')
                await self.clear_user_session(user_id)
                return

            try:
                min_amount, max_amount = text.split('-')
                min_amount = int(min_amount.strip())
                max_amount = int(max_amount.strip())

                if min_amount <= 0 or max_amount <= 0 or min_amount > max_amount:
                    raise ValueError("Invalid range")

            except ValueError:
                await update.message.reply_text("❌ Invalid range. Please enter valid numbers.", parse_mode='HTML')
                await self.clear_user_session(user_id)
                return

            from services.admin_service import AdminService
            admin_service = AdminService()

            # Update joining bonus amount setting
            success = await admin_service.update_admin_setting(user_id, 'joining_bonus_amount_range', text)

            if success:
                message = f"✅ <b>Joining Bonus Amount Updated</b>\n\n"
                message += f"🎁 <b>Joining bonus amount range set to:</b> ₹{text}"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏠 Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Error setting joining bonus amount.", parse_mode='HTML')

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_joining_bonus_amount_step2: {e}")
            await update.message.reply_text("❌ Something went wrong. Please try again later.", parse_mode='HTML')
            await self.clear_user_session(user_id)
    
    async def _handle_check_user_record_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        pass
    
    async def _handle_pass_user_withdrawal_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        pass
    
    async def _handle_fail_user_withdrawal_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        pass
    
    async def _handle_broadcast_gift_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        pass
    
    async def _handle_broadcast_gift_invite_link_step(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        pass
    
    async def _handle_broadcast_gift_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        pass
    

    

    
    async def _handle_set_account_ifsc_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set account IFSC step 2 with automatic bank details fetching"""
        user_id = update.effective_user.id
        text = update.message.text.strip().upper()

        try:
            # First, validate IFSC format
            from utils.helpers import is_valid_ifsc_code, fetch_bank_details_by_ifsc, format_bank_details_message

            if not is_valid_ifsc_code(text):
                await update.message.reply_text(
                    "❌ <b>Invalid IFSC Code Format</b>\n\n"
                    "Please enter a valid 11-character IFSC code.\n\n"
                    "📝 <b>Format:</b> 4 letters + 7 alphanumeric characters\n"
                    "💡 <b>Example:</b> SBIN0001234\n\n"
                    "Send /cancel to cancel this process.",
                    parse_mode='HTML'
                )
                return

            # Show processing message
            processing_msg = await update.message.reply_text(
                "🔍 <b>Validating IFSC Code...</b>\n\n"
                "Please wait while we fetch your bank details.",
                parse_mode='HTML'
            )

            # Fetch bank details from API
            bank_details = await fetch_bank_details_by_ifsc(text)

            if bank_details['success']:
                # Bank details found - show confirmation
                bank_message = format_bank_details_message(bank_details)

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('✅ Confirm Bank Details', callback_data=f'confirm_ifsc_{text}')],
                    [InlineKeyboardButton('❌ Re-enter IFSC', callback_data='reenter_ifsc')],
                    [InlineKeyboardButton('↩️ Back to Account Setup', callback_data='setAccountInfo')]
                ])

                # Store bank details in session for confirmation
                session_data = await self.get_user_session(user_id)
                if not session_data:
                    session_data = {'data': {}}

                # Ensure data field exists
                if 'data' not in session_data:
                    session_data['data'] = {}

                # Store bank details in session data
                session_data['data']['bank_details'] = bank_details

                # Update session with correct parameters
                await self.set_user_session(user_id, 'confirm_ifsc', session_data['data'])

                # Delete processing message and send bank details
                await processing_msg.delete()
                await update.message.reply_text(bank_message, reply_markup=keyboard, parse_mode='HTML')

            else:
                # Bank details not found or error occurred
                error_message = f"❌ <b>IFSC Validation Failed</b>\n\n{bank_details['message']}\n\n"

                if bank_details['error'] == 'ifsc_not_found':
                    error_message += "💡 <b>Please check your IFSC code and try again.</b>\n\n"
                    error_message += "🔍 You can find your IFSC code on:\n"
                    error_message += "• Your bank passbook\n"
                    error_message += "• Cheque book\n"
                    error_message += "• Bank's official website\n\n"
                elif bank_details['error'] in ['timeout', 'connection_error', 'api_error']:
                    error_message += "🔄 <b>Fallback Option:</b>\n"
                    error_message += "You can continue with manual entry if the service is unavailable.\n\n"

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='retry_ifsc')],
                    [InlineKeyboardButton('✏️ Manual Entry', callback_data='manual_bank_entry')],
                    [InlineKeyboardButton('↩️ Back to Account Setup', callback_data='setAccountInfo')]
                ])

                # Delete processing message and send error
                await processing_msg.delete()
                await update.message.reply_text(error_message, reply_markup=keyboard, parse_mode='HTML')

                # Keep session active for retry
                await self.set_user_session(user_id, 'set_account_ifsc')

        except Exception as e:
            logger.error(f"Error in _handle_set_account_ifsc_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error Processing IFSC</b>\n\n"
                "An unexpected error occurred while validating your IFSC code.\n\n"
                "Please try again or contact support if the issue persists.",
                parse_mode='HTML'
            )
            await self.clear_user_session(user_id)

    async def _handle_set_account_ifsc_manual_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle manual IFSC entry (fallback when API is unavailable)"""
        user_id = update.effective_user.id
        text = update.message.text.strip().upper()

        try:
            # Validate IFSC format only
            from utils.helpers import is_valid_ifsc_code

            if not is_valid_ifsc_code(text):
                await update.message.reply_text(
                    "❌ <b>Invalid IFSC Code Format</b>\n\n"
                    "Please enter a valid 11-character IFSC code.\n\n"
                    "📝 <b>Format:</b> 4 letters + 7 alphanumeric characters\n"
                    "💡 <b>Example:</b> SBIN0001234\n\n"
                    "Send /cancel to cancel this process.",
                    parse_mode='HTML'
                )
                return

            # Update account info with IFSC (manual entry)
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            if await withdrawal_service.update_account_info(user_id, 'ifsc', text):
                await self.clear_user_session(user_id)

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📝 Continue Setup', callback_data='setAccountInfo')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                success_message = "✅ <b>IFSC Code Saved</b>\n"
                success_message += "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
                success_message += f"🏛️ <b>IFSC Code:</b> {text}\n\n"
                success_message += "✅ Your IFSC code has been saved successfully.\n\n"
                success_message += "⚠️ <b>Note:</b> Bank details were entered manually.\n"
                success_message += "Please ensure the IFSC code is correct.\n\n"
                success_message += "📝 <b>Next Steps:</b>\n"
                success_message += "• Enter your account number\n"
                success_message += "• Provide your name as per bank records\n"
                success_message += "• Add email and mobile number"

                await update.message.reply_text(success_message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(
                    "❌ <b>Failed to Save IFSC Code</b>\n\n"
                    "Please try again or contact support.",
                    parse_mode='HTML'
                )
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_account_ifsc_manual_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error Saving IFSC Code</b>\n\n"
                "An unexpected error occurred.\n\n"
                "Please try again or contact support.",
                parse_mode='HTML'
            )
            await self.clear_user_session(user_id)

    async def _handle_set_account_email_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set account email step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text.strip().lower()

        try:
            # Validate email
            from utils.helpers import is_valid_email
            if not is_valid_email(text):
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set email')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Invalid email format. Please enter a valid email address.\n\nExample: <EMAIL>",
                    reply_markup=keyboard
                )
                return

            # Update account info
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            if await withdrawal_service.update_account_info(user_id, 'email', text):
                await self.clear_user_session(user_id)

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📝 Continue Setup', callback_data='setAccountInfo')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                success_message = f"✅ <b>Email Updated</b>\n\n"
                success_message += f"📧 <b>Email:</b> {text}\n\n"
                success_message += "Your email address has been updated successfully."

                await update.message.reply_text(success_message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Failed to update email. Please try again.")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_account_email_step2: {e}")
            await update.message.reply_text("❌ Error updating email. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_set_account_number_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set account number step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        try:
            # Validate account number format
            from utils.helpers import is_valid_account_number
            if not is_valid_account_number(text):
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set account_number')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Invalid account number format. Please enter a valid account number.\n\nExample: *********0",
                    reply_markup=keyboard
                )
                return

            # Check for duplicate account number
            from models.withdrawal import WithdrawalModel
            uniqueness_validation = await WithdrawalModel.validate_unique_account_number(text, user_id)

            if not uniqueness_validation['valid']:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set account_number')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])

                await update.message.reply_text(
                    "❌ Sorry, this account number is already registered by another user. Please enter a different account number.",
                    reply_markup=keyboard
                )
                await self.clear_user_session(user_id)
                return

            # Update account info
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            if await withdrawal_service.update_account_info(user_id, 'account_number', text):
                await self.clear_user_session(user_id)

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📝 Continue Setup', callback_data='setAccountInfo')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                success_message = f"✅ <b>Account Number Updated</b>\n\n"
                success_message += f"🔢 <b>Account Number:</b> {text}\n\n"
                success_message += "Your account number has been updated successfully."

                await update.message.reply_text(success_message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Failed to update account number. Please try again.")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_account_number_step2: {e}")
            await update.message.reply_text("❌ Error updating account number. Please try again.")
            await self.clear_user_session(user_id)
    
    async def _handle_set_mobile_number_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set mobile number step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        try:
            # Validate mobile number
            from utils.helpers import is_valid_mobile_number
            if not is_valid_mobile_number(text):
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set mobile_number')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Invalid mobile number format. Please enter a valid mobile number with country code.\n\nExample: +************",
                    reply_markup=keyboard
                )
                return

            # Send OTP
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            logger.info(f"Attempting to send OTP to mobile number: {text}")
            otp = await withdrawal_service.send_otp(text)

            if otp:
                # Store OTP and mobile number in session
                otp_data = {
                    'mobile_number': text,
                    'otp': otp,
                    'otp_timestamp': get_current_timestamp()
                }

                await self.set_user_session(user_id, 'verify_otp', otp_data)

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                # Enhanced message with delivery timing information
                message = f"📱 <b>OTP Verification</b>\n\n"
                message += f"📞 <b>Mobile Number:</b> {text}\n\n"
                message += "🔐 An OTP has been sent to your mobile number.\n"

                message += "Send /cancel to cancel the process."

                # Add resend option
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Resend OTP', callback_data='resend_otp')],
                    [InlineKeyboardButton('❌ Cancel', callback_data='cancel_otp')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
                logger.info(f"OTP API call successful for {text}, user {user_id} moved to verification step")

            else:
                logger.error(f"Failed to send OTP to {text} for user {user_id}")

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set mobile_number')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])

                error_message = "❌ <b>Failed to Send OTP</b>\n\n"
                error_message += "We couldn't send an OTP to your mobile number. This could be due to:\n\n"
                error_message += "• Invalid mobile number format\n"
                error_message += "• Network connectivity issues\n"
                error_message += "• OTP service temporarily unavailable\n\n"
                error_message += "Please check your mobile number format (+countrycode followed by number) and try again.\n\n"
                error_message += f"📞 <b>Number entered:</b> {text}"

                await update.message.reply_text(error_message, reply_markup=keyboard, parse_mode='HTML')
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_mobile_number_step2: {e}")
            await update.message.reply_text("❌ Error processing mobile number. Please try again.")
            await self.clear_user_session(user_id)

    async def handle_resend_otp(self, update, context):
        """Handle OTP resend request"""
        try:
            query = update.callback_query
            await query.answer()

            user_id = query.from_user.id

            # Get current session data
            session_data = await self.get_user_session(user_id)

            if not session_data or session_data.get('step') != 'verify_otp':
                await query.edit_message_text(
                    "❌ No active OTP verification session found. Please start the mobile number setup again.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('🔄 Start Over', callback_data='set mobile_number')]
                    ])
                )
                return

            mobile_number = session_data.get('data', {}).get('mobile_number')

            if not mobile_number:
                await query.edit_message_text(
                    "❌ Mobile number not found in session. Please start over.",
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton('🔄 Start Over', callback_data='set mobile_number')]
                    ])
                )
                return

            # Send new OTP
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            logger.info(f"Resending OTP to mobile number: {mobile_number} for user {user_id}")
            otp = await withdrawal_service.send_otp(mobile_number)

            if otp:
                # Update session with new OTP
                from utils.helpers import get_current_timestamp
                otp_data = {
                    'mobile_number': mobile_number,
                    'otp': otp,
                    'otp_timestamp': get_current_timestamp()
                }

                await self.set_user_session(user_id, 'verify_otp', otp_data)

                message = f"📱 <b>OTP Resent</b>\n\n"
                message += f"📞 <b>Mobile Number:</b> {mobile_number}\n\n"
                message += "✅ A new OTP has been sent to your mobile number.\n\n"
                message += "📨 <b>Delivery Time:</b> Usually 1-5 minutes\n"
                message += "🔄 <b>Previous OTP:</b> No longer valid\n\n"
                message += "Please enter the new 4-digit OTP when you receive it:\n\n"
                message += "⏰ <b>New OTP expires in 5 minutes</b>\n\n"
                message += "Send /cancel to cancel the process."

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Resend Again', callback_data='resend_otp')],
                    [InlineKeyboardButton('❌ Cancel', callback_data='cancel_otp')]
                ])

                await query.edit_message_text(message, reply_markup=keyboard, parse_mode='HTML')
                logger.info(f"OTP resent successfully to {mobile_number} for user {user_id}")

            else:
                logger.error(f"Failed to resend OTP to {mobile_number} for user {user_id}")

                error_message = "❌ <b>Failed to Resend OTP</b>\n\n"
                error_message += "We couldn't resend the OTP. This could be due to:\n\n"
                error_message += "• Temporary network issues\n"
                error_message += "• SMS service rate limiting\n"
                error_message += "• Carrier restrictions\n\n"
                error_message += "Please wait a few minutes and try again, or use a different number."

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='resend_otp')],
                    [InlineKeyboardButton('📱 Change Number', callback_data='set mobile_number')],
                    [InlineKeyboardButton('❌ Cancel', callback_data='cancel_otp')]
                ])

                await query.edit_message_text(error_message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in resend OTP: {e}")
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            await query.edit_message_text(
                "❌ An error occurred while resending OTP. Please try again.",
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='resend_otp')]
                ])
            )

    async def _handle_verify_otp_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle OTP verification step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        try:
            mobile_number = data.get('mobile_number', '')
            expected_otp = data.get('otp', '')
            otp_timestamp = data.get('otp_timestamp', 0)

            # Validate OTP format
            if not text.isdigit() or len(text) != 4:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Resend OTP', callback_data='resend_otp')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Invalid OTP format. Please enter a 4-digit OTP.",
                    reply_markup=keyboard
                )
                return

            # Verify OTP
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            if await withdrawal_service.validate_otp(text, expected_otp, otp_timestamp):
                # OTP verified, update mobile number
                if await withdrawal_service.update_account_info(user_id, 'mobile_number', mobile_number):
                    await self.clear_user_session(user_id)

                    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('📝 Continue Setup', callback_data='setAccountInfo')],
                        [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                    ])

                    success_message = f"✅ <b>Mobile Number Verified</b>\n\n"
                    success_message += f"📱 <b>Mobile Number:</b> {mobile_number}\n\n"
                    success_message += "🎉 Your mobile number has been verified and updated successfully!"

                    await update.message.reply_text(success_message, reply_markup=keyboard, parse_mode='HTML')
                else:
                    await update.message.reply_text("❌ Failed to update mobile number. Please try again.")
                    await self.clear_user_session(user_id)
            else:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Resend OTP', callback_data='resend_otp')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Invalid or expired OTP. Please try again.",
                    reply_markup=keyboard
                )

        except Exception as e:
            logger.error(f"Error in _handle_verify_otp_step2: {e}")
            await update.message.reply_text("❌ Error verifying OTP. Please try again.")
            await self.clear_user_session(user_id)
    
    async def _handle_add_force_sub_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        pass
    
    async def _handle_add_task_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add task step 2 - get task name (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate task name
            if len(text) < 3:
                await update.message.reply_text(
                    "❌ Task name is too short. Please enter at least 3 characters.\n\nSend /cancel to cancel."
                )
                return

            if len(text) > 50:
                await update.message.reply_text(
                    "❌ Task name is too long. Please keep it under 50 characters.\n\nSend /cancel to cancel."
                )
                return

            # Update session with task name
            await self.update_session_data(user_id, {'task_name': text})

            message = "➕ <b>Add New Task</b>\n\n"
            message += f"✅ <b>Task Name:</b> {text}\n\n"
            message += "📝 <b>Step 2:</b> Enter the task description\n"
            message += "Provide detailed instructions for users (10-500 characters).\n\n"
            message += "Send /cancel to cancel the process."

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'add_task_description', {'task_name': text})

        except Exception as e:
            logger.error(f"Error in _handle_add_task_step2: {e}")
            await update.message.reply_text("❌ Error processing task name. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_add_task_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle add task step 3 - get task description (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate task description
            if len(text) < 10:
                await update.message.reply_text(
                    "❌ Task description is too short. Please enter at least 10 characters.\n\nSend /cancel to cancel."
                )
                return

            if len(text) > 500:
                await update.message.reply_text(
                    "❌ Task description is too long. Please keep it under 500 characters.\n\nSend /cancel to cancel."
                )
                return

            task_name = data.get('task_name', '')

            message = "➕ <b>Add New Task</b>\n\n"
            message += f"✅ <b>Task Name:</b> {task_name}\n"
            message += f"✅ <b>Description:</b> {text}\n\n"
            message += "💰 <b>Step 3:</b> Enter the reward amount\n"
            message += "How much should users earn for completing this task? (in ₹)\n\n"
            message += "Send /cancel to cancel the process."

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'add_task_reward', {
                'task_name': task_name,
                'task_description': text
            })

        except Exception as e:
            logger.error(f"Error in _handle_add_task_step3: {e}")
            await update.message.reply_text("❌ Error processing task description. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_add_task_step4(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle add task step 4 - get reward amount (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate reward amount
            try:
                reward_amount = int(text)
                if reward_amount <= 0:
                    raise ValueError("Amount must be positive")
                if reward_amount > 10000:
                    raise ValueError("Amount too high")
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid amount. Please enter a valid number between 1 and 10000.\n\nSend /cancel to cancel."
                )
                return

            task_name = data.get('task_name', '')
            task_description = data.get('task_description', '')

            message = "➕ <b>Add New Task</b>\n\n"
            message += f"✅ <b>Task Name:</b> {task_name}\n"
            message += f"✅ <b>Description:</b> {task_description}\n"
            message += f"✅ <b>Reward:</b> ₹{reward_amount}\n\n"
            message += "📸 <b>Step 4:</b> Add task media (optional)\n"
            message += "Send a photo or document to help users understand the task.\n"
            message += "Or type 'skip' to skip this step.\n\n"
            message += "Send /cancel to cancel the process."

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'add_task_media', {
                'task_name': task_name,
                'task_description': task_description,
                'reward_amount': reward_amount
            })

        except Exception as e:
            logger.error(f"Error in _handle_add_task_step4: {e}")
            await update.message.reply_text("❌ Error processing reward amount. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_add_task_step5(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle add task step 5 - get media or skip (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        message = update.message

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            task_name = data.get('task_name', '')
            task_description = data.get('task_description', '')
            reward_amount = data.get('reward_amount', 0)

            media_url = ""

            # Check if user wants to skip media
            if message.text and message.text.strip().lower() == 'skip':
                media_url = ""
            elif message.photo:
                # Get the highest resolution photo
                media_url = message.photo[-1].file_id
            elif message.document:
                media_url = message.document.file_id
            else:
                await update.message.reply_text(
                    "❌ Please send a photo, document, or type 'skip' to skip this step.\n\nSend /cancel to cancel."
                )
                return

            # Show final confirmation
            confirmation_message = "➕ <b>Add New Task - Final Review</b>\n\n"
            confirmation_message += f"📝 <b>Name:</b> {task_name}\n"
            confirmation_message += f"📋 <b>Description:</b> {task_description}\n"
            confirmation_message += f"💰 <b>Reward:</b> ₹{reward_amount}\n"
            confirmation_message += f"📸 <b>Media:</b> {'Yes' if media_url else 'No'}\n\n"
            confirmation_message += "Choose the task status:"

            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('✅ Active (Users can see)', callback_data='setTaskStatus_active')],
                [InlineKeyboardButton('❌ Inactive (Hidden)', callback_data='setTaskStatus_inactive')]
            ])

            await update.message.reply_text(
                confirmation_message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

            # Update session with all data
            await self.set_user_session(user_id, 'add_task_confirm', {
                'task_name': task_name,
                'task_description': task_description,
                'reward_amount': reward_amount,
                'media_url': media_url
            })

        except Exception as e:
            logger.error(f"Error in _handle_add_task_step5: {e}")
            await update.message.reply_text("❌ Error processing media. Please try again.")
            await self.clear_user_session(user_id)
    
    async def _handle_generate_gift_code_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        pass
    
    async def _handle_generate_gift_code_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        pass
    
    async def _handle_generate_gift_code_step4(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        pass
    
    async def _handle_configure_level_referrals_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        pass
    
    async def _handle_configure_level_bonuses_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        pass
    
    async def _handle_set_withdrawal_tax_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set withdrawal tax step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            # Get session data
            session_data = await self.get_user_session(user_id)
            if not session_data:
                await update.message.reply_text("❌ Session expired. Please try again.", parse_mode='HTML')
                return

            # Call admin handler
            await self.admin_handlers.handle_set_withdrawal_tax_step2(update, context, text, session_data)

        except Exception as e:
            logger.error(f"Error in _handle_set_withdrawal_tax_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
    
    async def _handle_set_binance_id_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle set Binance ID step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        try:
            # Validate Binance ID
            if len(text) < 5:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set USDTAddress')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Binance ID is too short. Please enter a valid Binance ID.\n\nExample: *********",
                    reply_markup=keyboard
                )
                return

            if len(text) > 50:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set USDTAddress')],
                    [InlineKeyboardButton('↩️ Back to Account Info', callback_data='setAccountInfo')]
                ])
                await update.message.reply_text(
                    "❌ Binance ID is too long. Please enter a valid Binance ID.",
                    reply_markup=keyboard
                )
                return

            # Update account info
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            if await withdrawal_service.update_account_info(user_id, 'binance_id', text):
                await self.clear_user_session(user_id)

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📝 Continue Setup', callback_data='setAccountInfo')],
                    [InlineKeyboardButton('💰 My Wallet', callback_data='myWallet')]
                ])

                success_message = f"✅ <b>Binance ID Updated</b>\n\n"
                success_message += f"₿ <b>Binance ID:</b> {text}\n\n"
                success_message += "Your Binance ID has been updated successfully.\n"
                success_message += "You can now make USDT withdrawals!"

                await update.message.reply_text(success_message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Failed to update Binance ID. Please try again.")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_set_binance_id_step2: {e}")
            await update.message.reply_text("❌ Error updating Binance ID. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_broadcast_message_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle broadcast message step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        message = update.message

        # Debug logging
        logger.info(f"DEBUG: _handle_broadcast_message_step2 called for user {user_id}")
        logger.info(f"DEBUG: Message type - text: {bool(message.text)}, photo: {bool(message.photo)}, video: {bool(message.video)}, document: {bool(message.document)}, audio: {bool(message.audio)}")

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Prepare message data based on message type
            message_data = {}

            if message.text:
                message_data = {
                    "type": "text",
                    "text": message.text
                }
            elif message.photo:
                message_data = {
                    "type": "photo",
                    "photo": message.photo[-1].file_id,
                    "caption": message.caption or ""
                }
            elif message.video:
                message_data = {
                    "type": "video",
                    "video": message.video.file_id,
                    "caption": message.caption or ""
                }
            elif message.document:
                message_data = {
                    "type": "document",
                    "document": message.document.file_id,
                    "caption": message.caption or ""
                }
            elif message.audio:
                message_data = {
                    "type": "audio",
                    "audio": message.audio.file_id,
                    "caption": message.caption or ""
                }
            else:
                await update.message.reply_text(
                    "❌ Unsupported message type. Please send text, photo, video, document, or audio.\n\nSend /cancel to cancel."
                )
                return

            # Start broadcast
            from services.admin_service import AdminService
            admin_service = AdminService()

            result = await admin_service.start_message_broadcast(user_id, message_data)

            if result["success"]:
                # Clear session
                await self.clear_user_session(user_id)

                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    result["message"],
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(f"❌ {result['error']}")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_broadcast_message_step2: {e}")
            await update.message.reply_text("❌ Error starting broadcast. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_gift_broadcast_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle gift broadcast channel step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Parse channel input
            channel_data = await self._parse_channel_input(text)

            if not channel_data:
                await update.message.reply_text(
                    "❌ Invalid channel format. Please enter a valid channel username or invite link.\n\nExamples:\n• @channelname\n• https://t.me/channelname\n• https://t.me/+invitelink\n\nSend /cancel to cancel."
                )
                return

            message = f"🎁 <b>Broadcast Gift - Step 2</b>\n\n"
            message += f"✅ <b>Channel:</b> {channel_data.get('title', channel_data.get('username', 'Unknown'))}\n\n"
            message += f"💰 <b>Step 2:</b> Enter the gift amount (in ₹)\n\n"
            message += f"💡 <b>Example:</b> 50\n\n"
            message += f"Send /cancel to cancel the process."

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'gift_broadcast_amount', channel_data)

        except Exception as e:
            logger.error(f"Error in _handle_gift_broadcast_channel_step2: {e}")
            await update.message.reply_text("❌ Error processing channel. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_gift_broadcast_amount_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle gift broadcast amount step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate amount
            try:
                amount = float(text)
                if amount <= 0:
                    raise ValueError("Amount must be positive")
                if amount > 10000:
                    raise ValueError("Amount too high")
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid amount. Please enter a valid number between 1 and 10000.\n\nSend /cancel to cancel."
                )
                return

            channel_data = data
            channel_title = channel_data.get('title', channel_data.get('username', 'Unknown'))

            # Start gift broadcast
            from services.admin_service import AdminService
            admin_service = AdminService()

            result = await admin_service.start_gift_broadcast(
                user_id, channel_title, amount, channel_data
            )

            if result["success"]:
                # Clear session
                await self.clear_user_session(user_id)

                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    result["message"],
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(f"❌ {result['error']}")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_gift_broadcast_amount_step2: {e}")
            await update.message.reply_text("❌ Error starting gift broadcast. Please try again.")
            await self.clear_user_session(user_id)

    async def _parse_channel_input(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse channel input and return channel data"""
        try:
            # Remove whitespace
            text = text.strip()

            if text.startswith('@'):
                # Username format: @channelname
                username = text[1:]  # Remove @
                return {
                    "type": "public",
                    "username": username,
                    "title": f"@{username}",
                    "id": f"@{username}",
                    "invite_link": f"https://t.me/{username}"
                }

            elif text.startswith('https://t.me/+'):
                # Private channel invite link
                return {
                    "type": "private",
                    "title": "Private Channel",
                    "invite_link": text,
                    "id": text
                }

            elif text.startswith('https://t.me/'):
                # Public channel link: https://t.me/channelname
                username = text.replace('https://t.me/', '')
                return {
                    "type": "public",
                    "username": username,
                    "title": f"@{username}",
                    "id": f"@{username}",
                    "invite_link": text
                }

            elif text.startswith('t.me/'):
                # Short format: t.me/channelname
                username = text.replace('t.me/', '')
                return {
                    "type": "public",
                    "username": username,
                    "title": f"@{username}",
                    "id": f"@{username}",
                    "invite_link": f"https://t.me/{username}"
                }

            else:
                # Assume it's a username without @
                return {
                    "type": "public",
                    "username": text,
                    "title": f"@{text}",
                    "id": f"@{text}",
                    "invite_link": f"https://t.me/{text}"
                }

        except Exception as e:
            logger.error(f"Error parsing channel input: {e}")
            return None

    async def _handle_configure_level_settings_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle configure level settings step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Parse configuration input
            config_data = self._parse_level_config_input(text)

            if not config_data:
                await update.message.reply_text(
                    "❌ Invalid configuration format. Please use the correct format:\n\n"
                    "<code>referrals: 1,5,10,15,20,25\n"
                    "bonuses: 2,10,15,20,25,30</code>\n\n"
                    "Send /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            # Validate configuration
            from models.level_rewards import LevelRewardsModel
            validation = LevelRewardsModel.validate_config(config_data)

            if not validation['valid']:
                error_message = "❌ Configuration validation failed:\n\n"
                for error in validation['errors']:
                    error_message += f"• {error}\n"
                error_message += "\nPlease correct the errors and try again.\nSend /cancel to cancel."

                await update.message.reply_text(error_message)
                return

            # Update configuration
            from services.level_rewards_service import LevelRewardsService
            level_rewards_service = LevelRewardsService()

            success = await level_rewards_service.update_level_rewards_config(
                config_data['referral_requirements'],
                config_data['bonus_amounts']
            )

            if success:
                # Clear session
                await self.clear_user_session(user_id)

                # Show success message
                success_message = LevelRewardsModel.format_config_update_success_message(
                    config_data['referral_requirements'],
                    config_data['bonus_amounts']
                )

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏆 Configure Again', callback_data='configureLevelRewards')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text("❌ Error updating configuration. Please try again.")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_configure_level_settings_step2: {e}")
            await update.message.reply_text("❌ Error processing configuration. Please try again.")
            await self.clear_user_session(user_id)

    def _parse_level_config_input(self, text: str) -> Optional[Dict[str, Any]]:
        """Parse level configuration input"""
        try:
            lines = text.strip().split('\n')

            if len(lines) != 2:
                return None

            referrals_line = lines[0].strip()
            bonuses_line = lines[1].strip()

            # Parse referrals line
            if not referrals_line.startswith('referrals:'):
                return None

            referrals_str = referrals_line.replace('referrals:', '').strip()
            referrals = [int(x.strip()) for x in referrals_str.split(',')]

            # Parse bonuses line
            if not bonuses_line.startswith('bonuses:'):
                return None

            bonuses_str = bonuses_line.replace('bonuses:', '').strip()
            bonuses = [float(x.strip()) for x in bonuses_str.split(',')]

            return {
                'referral_requirements': referrals,
                'bonus_amounts': bonuses
            }

        except Exception as e:
            logger.error(f"Error parsing level config input: {e}")
            return None

    async def _handle_generate_gift_code_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle generate gift code step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate code format
            from models.gift_code import GiftCodeModel
            validation = GiftCodeModel.validate_gift_code_format(text)

            if not validation['valid']:
                for error in validation['errors']:
                    await update.message.reply_text(f"❌ {error}")
                return

            # Check if code already exists
            from services.gift_code_service import GiftCodeService
            gift_code_service = GiftCodeService()

            if await gift_code_service.check_code_exists(text):
                await update.message.reply_text("❌ This code already exists. Please choose a different code.")
                return

            # Show step 2 message
            message = GiftCodeModel.format_gift_code_generation_step2_message(text)

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'generate_gift_amount', {'code': text})

        except Exception as e:
            logger.error(f"Error in _handle_generate_gift_code_step2: {e}")
            await update.message.reply_text("❌ Error processing code. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_generate_gift_amount_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle generate gift amount step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate amount
            from models.gift_code import GiftCodeModel
            validation = GiftCodeModel.validate_gift_amount(text)

            if not validation['valid']:
                for error in validation['errors']:
                    await update.message.reply_text(f"❌ {error}")
                return

            amount = validation['amount']
            code = data['code']

            # Show step 3 message
            message = GiftCodeModel.format_gift_code_generation_step3_message(code, amount)

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'generate_gift_limit', {
                'code': code,
                'amount': amount
            })

        except Exception as e:
            logger.error(f"Error in _handle_generate_gift_amount_step2: {e}")
            await update.message.reply_text("❌ Error processing amount. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_generate_gift_limit_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle generate gift limit step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate usage limit
            from models.gift_code import GiftCodeModel
            validation = GiftCodeModel.validate_usage_limit(text)

            if not validation['valid']:
                for error in validation['errors']:
                    await update.message.reply_text(f"❌ {error}")
                return

            usage_limit = validation['usage_limit']
            code = data['code']
            amount = data['amount']

            # Create gift code
            code_data = GiftCodeModel.create_gift_code(
                code=code,
                amount=amount,
                usage_limit=usage_limit,
                created_by=user_id,
                expiry_date=0  # No expiry for now
            )

            # Save gift code
            from services.gift_code_service import GiftCodeService
            gift_code_service = GiftCodeService()

            if await gift_code_service.add_gift_code(code_data):
                # Clear session
                await self.clear_user_session(user_id)

                # Show success message
                success_message = GiftCodeModel.format_gift_code_creation_success_message(
                    code, amount, usage_limit
                )

                keyboard = GiftCodeModel.create_gift_code_generation_success_keyboard()

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text("❌ Error creating gift code. Please try again.")
                await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_generate_gift_limit_step2: {e}")
            await update.message.reply_text("❌ Error creating gift code. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_redeem_gift_code_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle redeem gift code step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            # Redeem gift code
            from services.gift_code_service import GiftCodeService
            gift_code_service = GiftCodeService()

            result = await gift_code_service.redeem_gift_code(text, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                # Show success message
                from models.gift_code import GiftCodeModel

                success_message = GiftCodeModel.format_gift_code_redemption_success_message(
                    result['amount']
                )

                keyboard = GiftCodeModel.create_gift_code_redemption_success_keyboard()

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Show failure message
                failure_message = GiftCodeModel.format_gift_code_redemption_failure_message(
                    result['message']
                )

                await update.message.reply_text(failure_message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_redeem_gift_code_step2: {e}")
            await update.message.reply_text("❌ Error processing gift code. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_add_balance_user_id_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add balance user ID step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate user ID
            try:
                target_user_id = int(text)
            except ValueError:
                await update.message.reply_text("❌ Invalid User ID. Please enter a valid number.")
                return

            # Check if user exists
            from services.user_service import UserService
            user_service = UserService()
            target_user = await user_service.get_user(target_user_id)

            if not target_user:
                await update.message.reply_text(f"❌ User with ID {target_user_id} not found.")
                return

            # Show amount input message
            message = f"💰 <b>Add Balance to User</b>\n\n"
            message += f"👤 <b>User:</b> {target_user.get('first_name', 'Unknown')} (ID: {target_user_id})\n"
            message += f"💳 <b>Current Balance:</b> ₹{target_user.get('balance', 0)}\n\n"
            message += f"💰 <b>Enter amount to add:</b>\n"
            message += f"Send /cancel to cancel the process."

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'add_balance_amount', {'target_user_id': target_user_id})

        except Exception as e:
            logger.error(f"Error in _handle_add_balance_user_id_step2: {e}")
            await update.message.reply_text("❌ Error processing user ID. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_add_balance_amount_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle add balance amount step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate amount
            try:
                amount = float(text)
                if amount <= 0:
                    raise ValueError("Amount must be positive")
                if amount > 100000:  # Reasonable upper limit
                    raise ValueError("Amount too high")
            except ValueError:
                await update.message.reply_text("❌ Invalid amount. Please enter a positive number.")
                return

            target_user_id = data['target_user_id']

            # Add balance
            from services.admin_user_service import AdminUserService
            admin_user_service = AdminUserService()

            result = await admin_user_service.add_user_balance(target_user_id, amount, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('💰 Add More Balance', callback_data='addBalance')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    f"✅ <b>Balance Added Successfully!</b>\n\n{result['message']}",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Error in _handle_add_balance_amount_step2: {e}")
            await update.message.reply_text("❌ Error adding balance. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_remove_balance_user_id_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove balance user ID step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate user ID
            try:
                target_user_id = int(text)
            except ValueError:
                await update.message.reply_text("❌ Invalid User ID. Please enter a valid number.")
                return

            # Check if user exists
            from services.user_service import UserService
            user_service = UserService()
            target_user = await user_service.get_user(target_user_id)

            if not target_user:
                await update.message.reply_text(f"❌ User with ID {target_user_id} not found.")
                return

            # Show amount input message
            message = f"💰 <b>Remove Balance from User</b>\n\n"
            message += f"👤 <b>User:</b> {target_user.get('first_name', 'Unknown')} (ID: {target_user_id})\n"
            message += f"💳 <b>Current Balance:</b> ₹{target_user.get('balance', 0)}\n\n"
            message += f"💰 <b>Enter amount to remove:</b>\n"
            message += f"Send /cancel to cancel the process."

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'remove_balance_amount', {'target_user_id': target_user_id})

        except Exception as e:
            logger.error(f"Error in _handle_remove_balance_user_id_step2: {e}")
            await update.message.reply_text("❌ Error processing user ID. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_remove_balance_amount_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle remove balance amount step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate amount
            try:
                amount = float(text)
                if amount <= 0:
                    raise ValueError("Amount must be positive")
            except ValueError:
                await update.message.reply_text("❌ Invalid amount. Please enter a positive number.")
                return

            target_user_id = data['target_user_id']

            # Remove balance
            from services.admin_user_service import AdminUserService
            admin_user_service = AdminUserService()

            result = await admin_user_service.remove_user_balance(target_user_id, amount, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('💰 Remove More Balance', callback_data='removeBalance')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    f"✅ <b>Balance Removed Successfully!</b>\n\n{result['message']}",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Error in _handle_remove_balance_amount_step2: {e}")
            await update.message.reply_text("❌ Error removing balance. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_ban_user_id_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle ban user ID step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate user ID
            try:
                target_user_id = int(text)
            except ValueError:
                await update.message.reply_text("❌ Invalid User ID. Please enter a valid number.")
                return

            # Check if user exists
            from services.user_service import UserService
            user_service = UserService()
            target_user = await user_service.get_user(target_user_id)

            if not target_user:
                await update.message.reply_text(f"❌ User with ID {target_user_id} not found.")
                return

            # Show reason input message
            message = f"🚫 <b>Ban User</b>\n\n"
            message += f"👤 <b>User:</b> {target_user.get('first_name', 'Unknown')} (ID: {target_user_id})\n"
            message += f"📊 <b>Status:</b> " + ("🚫 Banned" if target_user.get('banned', False) else "✅ Active") + "\n\n"
            message += f"📝 <b>Enter ban reason (optional):</b>\n"
            message += f"Send 'skip' to ban without reason or /cancel to cancel."

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session step
            await self.set_user_session(user_id, 'ban_user_reason', {'target_user_id': target_user_id})

        except Exception as e:
            logger.error(f"Error in _handle_ban_user_id_step2: {e}")
            await update.message.reply_text("❌ Error processing user ID. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_ban_user_reason_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]):
        """Handle ban user reason step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            target_user_id = data['target_user_id']
            reason = None if text.lower() == 'skip' else text

            # Ban user
            from services.admin_user_service import AdminUserService
            admin_user_service = AdminUserService()

            result = await admin_user_service.ban_user(target_user_id, user_id, reason)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🚫 Ban Another User', callback_data='banUser')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    f"✅ <b>User Banned Successfully!</b>\n\n{result['message']}",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Error in _handle_ban_user_reason_step2: {e}")
            await update.message.reply_text("❌ Error banning user. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_unban_user_id_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle unban user ID step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate user ID
            try:
                target_user_id = int(text)
            except ValueError:
                await update.message.reply_text("❌ Invalid User ID. Please enter a valid number.")
                return

            # Unban user
            from services.admin_user_service import AdminUserService
            admin_user_service = AdminUserService()

            result = await admin_user_service.unban_user(target_user_id, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('✅ Unban Another User', callback_data='unbanUser')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    f"✅ <b>User Unbanned Successfully!</b>\n\n{result['message']}",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Error in _handle_unban_user_id_step2: {e}")
            await update.message.reply_text("❌ Error unbanning user. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_check_user_record_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle check user record step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Validate user ID
            try:
                target_user_id = int(text)
            except ValueError:
                await update.message.reply_text("❌ Invalid User ID. Please enter a valid number.")
                return

            # Get user record
            from services.admin_user_service import AdminUserService
            admin_user_service = AdminUserService()

            result = await admin_user_service.get_user_record(target_user_id)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                user_data = result['user']
                stats = result['statistics']

                # Format user record message
                message = f"👤 <b>User Record</b>\n\n"
                message += f"🆔 <b>User ID:</b> {user_data['user_id']}\n"
                message += f"👤 <b>Name:</b> {user_data.get('first_name', 'Unknown')}\n"
                message += f"📱 <b>Username:</b> @{user_data.get('username', 'None')}\n"
                message += f"💳 <b>Balance:</b> ₹{user_data.get('balance', 0)}\n"
                message += f"📊 <b>Status:</b> " + ("🚫 Banned" if user_data.get('banned', False) else "✅ Active") + "\n\n"

                message += f"📈 <b>Statistics:</b>\n"
                message += f"🔗 <b>Referrals:</b> {stats['referral_count']}\n"
                message += f"💰 <b>Total Withdrawals:</b> {stats['total_withdrawals']}\n"
                message += f"💸 <b>Total Withdrawn:</b> ₹{stats['total_withdrawn']}\n"
                message += f"⏳ <b>Pending Withdrawals:</b> {stats['pending_withdrawals']} (₹{stats['pending_amount']})\n"
                message += f"❌ <b>Failed Withdrawals:</b> {stats['failed_withdrawals']}\n"
                message += f"📋 <b>Task Submissions:</b> {stats['task_submissions']}\n\n"

                # Add join date
                from datetime import datetime
                join_timestamp = user_data.get('created_at', 0)
                if join_timestamp:
                    join_date = datetime.fromtimestamp(join_timestamp).strftime('%Y-%m-%d %H:%M')
                    message += f"📅 <b>Joined:</b> {join_date}"

                # Show success message with keyboard
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('👤 Check Another User', callback_data='checkUserRecord')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                await update.message.reply_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(f"❌ {result['message']}")

        except Exception as e:
            logger.error(f"Error in _handle_check_user_record_step2: {e}")
            await update.message.reply_text("❌ Error checking user record. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_add_force_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle add force channel step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Parse channel input
            from services.force_subscription_service import ForceSubscriptionService
            force_sub_service = ForceSubscriptionService()

            channel_data = await force_sub_service.parse_channel_input(text)

            if not channel_data:
                await update.message.reply_text("❌ Invalid channel. Please check the channel exists and the bot has access to it.")
                return

            # Add channel data
            channel_data['added_by'] = user_id

            # Add force subscription channel
            if await force_sub_service.add_force_subscription_channel(channel_data):
                # Clear session
                await self.clear_user_session(user_id)

                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➕ Add Another Channel', callback_data='addForceChannel')],
                    [InlineKeyboardButton('📋 View All Channels', callback_data='viewForceChannels')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                success_message = f"✅ <b>Channel Added Successfully!</b>\n\n"
                success_message += f"📢 <b>Channel:</b> {channel_data.get('title', 'Unknown')}\n"
                success_message += f"📱 <b>Username:</b> @{channel_data.get('username', 'None')}\n"
                success_message += f"🔗 <b>Type:</b> {channel_data.get('type', 'unknown').title()}\n\n"
                success_message += f"Users must now join this channel to use the bot."

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text("❌ Channel already exists or failed to add. Please try a different channel.")

        except Exception as e:
            logger.error(f"Error in _handle_add_force_channel_step2: {e}")
            await update.message.reply_text("❌ Error adding channel. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_remove_force_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle remove force channel step 2 (matching PHP version exactly)"""
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        text = update.message.text.strip()

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.")
                await self.clear_user_session(user_id)
                return

            # Remove force subscription channel
            from services.force_subscription_service import ForceSubscriptionService
            force_sub_service = ForceSubscriptionService()

            if await force_sub_service.remove_force_subscription_channel(text):
                # Clear session
                await self.clear_user_session(user_id)

                # Show success message
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➖ Remove Another Channel', callback_data='removeForceChannel')],
                    [InlineKeyboardButton('📋 View All Channels', callback_data='viewForceChannels')],
                    [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
                ])

                success_message = f"✅ <b>Channel Removed Successfully!</b>\n\n"
                success_message += f"The channel has been removed from force subscription list.\n"
                success_message += f"Users no longer need to join this channel to use the bot."

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text("❌ Channel not found or failed to remove. Please check the channel identifier.")

        except Exception as e:
            logger.error(f"Error in _handle_remove_force_channel_step2: {e}")
            await update.message.reply_text("❌ Error removing channel. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_add_force_channel_forward_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle forwarded message for adding force subscription channel (matching PHP exactly)"""
        user_id = update.effective_user.id

        try:
            # Check if message is forwarded
            if not update.message.forward_from_chat:
                await update.message.reply_text(
                    "❌ <b>Invalid Message</b>\n\n"
                    "Please forward a message from the channel you want to add.\n\n"
                    "Send /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            forwarded_chat = update.message.forward_from_chat

            # Validate it's a channel
            if forwarded_chat.type not in ['channel', 'supergroup']:
                await update.message.reply_text(
                    "❌ <b>Invalid Channel Type</b>\n\n"
                    "Please forward a message from a channel or supergroup.\n\n"
                    "Send /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            # Extract channel information
            channel_id = str(forwarded_chat.id)
            channel_title = forwarded_chat.title
            channel_username = forwarded_chat.username

            # Verify bot has admin permissions
            try:
                bot_member = await context.bot.get_chat_member(forwarded_chat.id, context.bot.id)
                if bot_member.status not in ['administrator', 'creator']:
                    await update.message.reply_text(
                        "❌ <b>Insufficient Permissions</b>\n\n"
                        "The bot must be an administrator in the channel to add it to force subscription.\n\n"
                        "Please make the bot an admin and try again.\n\n"
                        "Send /cancel to cancel the process.",
                        parse_mode='HTML'
                    )
                    return
            except Exception as e:
                await update.message.reply_text(
                    "❌ <b>Permission Check Failed</b>\n\n"
                    "Unable to verify bot permissions in the channel.\n\n"
                    "Please ensure the bot is an administrator and try again.\n\n"
                    "Send /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            # Prepare channel data
            channel_data = {
                'channel_id': channel_id,
                'title': channel_title,
                'username': channel_username,
                'type': 'force_sub',
                'added_by': user_id
            }

            # Add channel to force subscription
            from services.force_subscription_service import ForceSubscriptionService
            force_sub_service = ForceSubscriptionService()

            success = await force_sub_service.add_force_subscription_channel(channel_data)

            if success:
                # Success message (matching PHP format)
                message = "✅ <b>Channel Added Successfully!</b>\n\n"
                message += f"📢 <b>Channel:</b> {channel_title}\n"
                message += f"🆔 <b>ID:</b> {channel_id}\n"
                if channel_username:
                    message += f"👤 <b>Username:</b> @{channel_username}\n"
                message += f"🤖 <b>Bot Status:</b> Administrator ✅\n\n"
                message += "The channel has been added to the force subscription list. All new users will need to join this channel."

                await update.message.reply_text(message, parse_mode='HTML')
            else:
                await update.message.reply_text(
                    "❌ Failed to add channel. It may already exist in the force subscription list.",
                    parse_mode='HTML'
                )

            # Clear session
            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_add_force_channel_forward_step2: {e}")
            await update.message.reply_text("❌ Error processing forwarded message. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_view_user_details_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle viewing user details step 2"""
        user_id = update.effective_user.id

        try:
            target_user_id = update.message.text.strip()

            # Validate user ID
            if not target_user_id.isdigit():
                await update.message.reply_text(
                    "❌ <b>Invalid User ID</b>\n\nPlease enter a valid numeric user ID.\n\nSend /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            target_user_id = int(target_user_id)

            # Get user details
            from services.user_service import UserService
            user_service = UserService()

            target_user = await user_service.get_user(target_user_id)
            if not target_user:
                await update.message.reply_text(
                    "❌ <b>User Not Found</b>\n\nNo user found with that ID.\n\nSend /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            # Format user details
            message = f"👤 <b>User Details</b>\n\n"
            message += f"🆔 <b>User ID:</b> {target_user_id}\n"
            message += f"👤 <b>Name:</b> {target_user.get('first_name', 'Unknown')} {target_user.get('last_name', '')}\n"
            message += f"💰 <b>Balance:</b> ₹{target_user.get('balance', 0)}\n"
            message += f"📅 <b>Joined:</b> {target_user.get('created_at', 'Unknown')}\n"
            message += f"🚫 <b>Banned:</b> {'Yes' if target_user.get('banned', False) else 'No'}\n\n"

            # Account info
            account_info = target_user.get('account_info', {})
            if account_info:
                withdrawal_method = account_info.get('withdrawal_method', 'Not set')
                message += f"💳 <b>Withdrawal Method:</b> {'Bank Account' if withdrawal_method == 'bank' else 'USDT (Binance ID)' if withdrawal_method == 'usdt' else 'Not set'}\n"

                if withdrawal_method == 'bank':
                    message += f"👤 <b>Account Name:</b> {account_info.get('name', 'Not set')}\n"
                    message += f"🏦 <b>Account Number:</b> {account_info.get('account_number', 'Not set')}\n"
                    message += f"🏛️ <b>IFSC Code:</b> {account_info.get('ifsc', 'Not set')}\n"
                    message += f"📧 <b>Email:</b> {account_info.get('email', 'Not set')}\n"
                    message += f"📱 <b>Mobile:</b> {account_info.get('mobile_number', 'Not set')}\n"
                elif withdrawal_method == 'usdt':
                    message += f"₿ <b>Binance ID:</b> {account_info.get('usdt_address', 'Not set')}\n"
            else:
                message += f"💳 <b>Account Info:</b> Not configured\n"

            # Referral info
            message += f"\n👥 <b>Referrals:</b> {len(target_user.get('referrals', []))}\n"
            message += f"🎁 <b>Joining Bonus:</b> {'Received' if target_user.get('joining_bonus_got', 0) else 'Not received'}"

            await update.message.reply_text(message, parse_mode='HTML')

            # Clear session
            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_view_user_details_step2: {e}")
            await update.message.reply_text("❌ Error retrieving user details. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_reset_user_method_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle resetting user withdrawal method step 2"""
        user_id = update.effective_user.id

        try:
            target_user_id = update.message.text.strip()

            # Validate user ID
            if not target_user_id.isdigit():
                await update.message.reply_text(
                    "❌ <b>Invalid User ID</b>\n\nPlease enter a valid numeric user ID.\n\nSend /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            target_user_id = int(target_user_id)

            # Get user details
            from services.user_service import UserService
            user_service = UserService()

            target_user = await user_service.get_user(target_user_id)
            if not target_user:
                await update.message.reply_text(
                    "❌ <b>User Not Found</b>\n\nNo user found with that ID.\n\nSend /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            # Reset withdrawal method
            account_info = target_user.get('account_info', {})
            current_method = account_info.get('withdrawal_method', 'Not set')

            # Clear account info to allow method change
            reset_account_info = {
                'withdrawal_method': None,
                'name': None,
                'account_number': None,
                'ifsc': None,
                'email': None,
                'mobile_number': None,
                'usdt_address': None
            }

            # Update user
            success = await user_service.update_user_account_info(target_user_id, reset_account_info)

            if success:
                message = f"✅ <b>Account Details Reset</b>\n\n"
                message += f"👤 <b>User ID:</b> {target_user_id}\n"
                message += f"👤 <b>Name:</b> {target_user.get('first_name', 'Unknown')}\n"
                message += f"🔄 <b>Previous Method:</b> {'Bank Account' if current_method == 'bank' else 'USDT (Binance ID)' if current_method == 'usdt' else 'Not set'}\n\n"
                message += f"✅ All account details have been cleared.\n"
                message += f"💡 <b>Note:</b> Users can change withdrawal methods anytime without admin intervention."
            else:
                message = f"❌ <b>Reset Failed</b>\n\nFailed to reset withdrawal method for user {target_user_id}."

            await update.message.reply_text(message, parse_mode='HTML')

            # Clear session
            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_reset_user_method_step2: {e}")
            await update.message.reply_text("❌ Error resetting user method. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_edit_task_name_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle edit task name step 2"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            session = await self.get_user_session(user_id)
            if not session or 'task_id' not in session:
                await update.message.reply_text("❌ Session expired. Please try again.", parse_mode='HTML')
                await self.clear_user_session(user_id)
                return

            task_id = session['task_id']

            if len(text) > 50:
                await update.message.reply_text("❌ Task name too long. Please keep it under 50 characters.")
                return

            from services.task_service import TaskService
            task_service = TaskService()

            if await task_service.update_task(task_id, {'name': text}):
                message = f"✅ <b>Task Name Updated</b>\n\n"
                message += f"📝 <b>New Name:</b> {text}\n\n"
                message += f"The task name has been updated successfully."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('✏️ Edit Task', callback_data=f'editTask_{task_id}')],
                    [InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Failed to update task name. Please try again.")

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_edit_task_name_step2: {e}")
            await update.message.reply_text("❌ Error updating task name. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_edit_task_description_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle edit task description step 2"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            session = await self.get_user_session(user_id)
            if not session or 'task_id' not in session:
                await update.message.reply_text("❌ Session expired. Please try again.", parse_mode='HTML')
                await self.clear_user_session(user_id)
                return

            task_id = session['task_id']

            from services.task_service import TaskService
            task_service = TaskService()

            if await task_service.update_task(task_id, {'description': text}):
                message = f"✅ <b>Task Description Updated</b>\n\n"
                message += f"📋 <b>New Description:</b>\n{text}\n\n"
                message += f"The task description has been updated successfully."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('✏️ Edit Task', callback_data=f'editTask_{task_id}')],
                    [InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Failed to update task description. Please try again.")

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_edit_task_description_step2: {e}")
            await update.message.reply_text("❌ Error updating task description. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_edit_task_reward_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle edit task reward step 2"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            session = await self.get_user_session(user_id)
            if not session or 'task_id' not in session:
                await update.message.reply_text("❌ Session expired. Please try again.", parse_mode='HTML')
                await self.clear_user_session(user_id)
                return

            task_id = session['task_id']

            # Validate reward amount
            try:
                reward_amount = int(text)
                if reward_amount <= 0:
                    await update.message.reply_text("❌ Reward amount must be a positive number.")
                    return
            except ValueError:
                await update.message.reply_text("❌ Invalid reward amount. Please enter numbers only.")
                return

            from services.task_service import TaskService
            task_service = TaskService()

            if await task_service.update_task(task_id, {'reward_amount': reward_amount}):
                message = f"✅ <b>Task Reward Updated</b>\n\n"
                message += f"💰 <b>New Reward:</b> ₹{reward_amount}\n\n"
                message += f"The task reward has been updated successfully."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('✏️ Edit Task', callback_data=f'editTask_{task_id}')],
                    [InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Failed to update task reward. Please try again.")

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_edit_task_reward_step2: {e}")
            await update.message.reply_text("❌ Error updating task reward. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_edit_task_media_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle edit task media step 2"""
        user_id = update.effective_user.id

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            session = await self.get_user_session(user_id)
            if not session or 'task_id' not in session:
                await update.message.reply_text("❌ Session expired. Please try again.", parse_mode='HTML')
                await self.clear_user_session(user_id)
                return

            task_id = session['task_id']
            media_url = ""

            # Check if user sent 'none' to remove media
            if update.message.text and update.message.text.lower() == 'none':
                media_url = ""
            elif update.message.photo:
                # Get the largest photo
                photo = update.message.photo[-1]
                file = await context.bot.get_file(photo.file_id)
                media_url = file.file_path
            else:
                await update.message.reply_text("❌ Please send a photo or 'none' to remove media.")
                return

            from services.task_service import TaskService
            task_service = TaskService()

            if await task_service.update_task(task_id, {'media_url': media_url}):
                if media_url:
                    message = f"✅ <b>Task Media Updated</b>\n\n"
                    message += f"🖼️ New media has been set for the task."
                else:
                    message = f"✅ <b>Task Media Removed</b>\n\n"
                    message += f"🖼️ Media has been removed from the task."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('✏️ Edit Task', callback_data=f'editTask_{task_id}')],
                    [InlineKeyboardButton('📋 Manage Tasks', callback_data='manageTasks')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text("❌ Failed to update task media. Please try again.")

            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_edit_task_media_step2: {e}")
            await update.message.reply_text("❌ Error updating task media. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_reset_user_account_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle reset user account step 2 - process user ID/username input"""
        user_id = update.effective_user.id
        text = update.message.text

        try:
            from utils.helpers import is_admin
            if not is_admin(user_id):
                await update.message.reply_text("❌ Access denied.", parse_mode='HTML')
                return

            # Parse user input (ID or username)
            target_user_id = None
            target_username = None

            if text.startswith('@'):
                target_username = text[1:]  # Remove @ symbol
            else:
                try:
                    target_user_id = int(text)
                except ValueError:
                    await update.message.reply_text("❌ Invalid input. Please enter a valid User ID (numbers) or Username (@username).")
                    return

            # Get user from database
            from services.user_service import UserService
            user_service = UserService()

            if target_user_id:
                target_user = await user_service.get_user(target_user_id)
            else:
                target_user = await user_service.get_user_by_username(target_username)

            if not target_user:
                await update.message.reply_text("❌ User not found. Please check the User ID or Username and try again.")
                return

            target_user_id = target_user['user_id']
            target_name = target_user.get('first_name', 'Unknown')
            target_username_display = target_user.get('username', 'No username')

            # Get current account info
            account_info = target_user.get('account_info', {})
            withdrawal_method = account_info.get('withdrawal_method', 'Not set')

            # Show confirmation message with user details
            message = f"🔄 <b>Reset User Account Details</b>\n\n"
            message += f"👤 <b>User Found:</b>\n"
            message += f"• <b>Name:</b> {target_name}\n"
            message += f"• <b>User ID:</b> {target_user_id}\n"
            message += f"• <b>Username:</b> @{target_username_display}\n\n"
            message += f"💳 <b>Current Withdrawal Method:</b> {withdrawal_method}\n\n"

            if account_info:
                message += f"📋 <b>Current Account Details:</b>\n"
                if withdrawal_method == 'bank':
                    message += f"• Account Name: {account_info.get('account_name', 'Not set')}\n"
                    message += f"• Account Number: {account_info.get('account_number', 'Not set')}\n"
                    message += f"• IFSC Code: {account_info.get('ifsc_code', 'Not set')}\n"
                    message += f"• Email: {account_info.get('email', 'Not set')}\n"
                elif withdrawal_method == 'usdt':
                    message += f"• Binance ID: {account_info.get('binance_id', 'Not set')}\n"
                message += "\n"

            message += f"⚠️ <b>Warning:</b> This action will:\n"
            message += f"• Clear all withdrawal method settings\n"
            message += f"• Remove all account details (bank/Binance)\n"
            message += f"• Allow user to set up withdrawal method again\n\n"
            message += f"❓ Are you sure you want to reset this user's account details?"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('✅ Yes, Reset Account', callback_data=f'confirmResetAccount_{target_user_id}')],
                [InlineKeyboardButton('❌ Cancel', callback_data='admin')]
            ])

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            await self.clear_user_session(user_id)

        except Exception as e:
            logger.error(f"Error in _handle_reset_user_account_step2: {e}")
            await update.message.reply_text("❌ Error processing user account reset. Please try again.")
            await self.clear_user_session(user_id)

    # ==================== NEW ENHANCED ADMIN PANEL SESSION HANDLERS ====================

    async def _handle_set_percent_tax_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle setting percentage tax - step 2"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        try:
            # Validate percentage
            try:
                percentage = float(text)
                if percentage < 0 or percentage > 100:
                    raise ValueError("Percentage must be between 0 and 100")
            except ValueError:
                # Add back button for navigation even on error
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Tax Configuration', callback_data='withdrawal_tax_config')]
                ])

                await update.message.reply_text(
                    "❌ <b>Invalid Input</b>\n\n"
                    "Please enter a valid percentage between 0 and 100.\n"
                    "Example: 30 (for 30% tax)",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Update withdrawal settings
            from services.admin_service import AdminService
            admin_service = AdminService()
            admin_settings = await admin_service.get_admin_settings()
            withdrawal_settings = admin_settings.get('withdrawal_settings', {})

            withdrawal_settings['tax_type'] = 'percentage'
            withdrawal_settings['tax_percentage'] = percentage
            withdrawal_settings['tax_amount'] = 0  # Clear fixed tax

            await admin_service.update_admin_setting('withdrawal_settings', withdrawal_settings)

            # Clear session
            await self.clear_user_session(user_id)

            # Success message
            message = f"✅ <b>Percentage Tax Updated</b>\n\n"
            message += f"📊 <b>New Settings:</b>\n"
            message += f"• Tax Type: Percentage\n"
            message += f"• Tax Rate: {percentage}%\n\n"
            message += f"💡 <b>Example:</b>\n"
            message += f"• User withdraws ₹100 → Receives ₹{100 - (100 * percentage / 100):.2f}\n"
            message += f"• User withdraws ₹500 → Receives ₹{500 - (500 * percentage / 100):.2f}"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Tax Configuration', callback_data='withdrawal_tax_config')]
            ])

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_set_percent_tax_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_set_fixed_tax_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle setting fixed tax amount - step 2"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        try:
            # Validate amount
            try:
                amount = float(text)
                if amount < 0:
                    raise ValueError("Amount must be positive")
            except ValueError:
                # Add back button for navigation even on error
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Tax Configuration', callback_data='withdrawal_tax_config')]
                ])

                await update.message.reply_text(
                    "❌ <b>Invalid Input</b>\n\n"
                    "Please enter a valid amount (₹).\n"
                    "Example: 5 (for ₹5 tax per withdrawal)",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Update withdrawal settings
            from services.admin_service import AdminService
            admin_service = AdminService()
            admin_settings = await admin_service.get_admin_settings()
            withdrawal_settings = admin_settings.get('withdrawal_settings', {})

            withdrawal_settings['tax_type'] = 'fixed'
            withdrawal_settings['tax_amount'] = amount
            withdrawal_settings['tax_percentage'] = 0  # Clear percentage tax

            await admin_service.update_admin_setting('withdrawal_settings', withdrawal_settings)

            # Clear session
            await self.clear_user_session(user_id)

            # Success message
            message = f"✅ <b>Fixed Tax Updated</b>\n\n"
            message += f"📊 <b>New Settings:</b>\n"
            message += f"• Tax Type: Fixed Amount\n"
            message += f"• Tax Amount: ₹{amount}\n\n"
            message += f"💡 <b>Example:</b>\n"
            message += f"• User withdraws ₹100 → Receives ₹{100 - amount:.2f}\n"
            message += f"• User withdraws ₹500 → Receives ₹{500 - amount:.2f}"

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Tax Configuration', callback_data='withdrawal_tax_config')]
            ])

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_set_fixed_tax_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_ban_user_withdrawal_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle ban/unban user withdrawal - step 2"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        try:
            # Validate user ID
            try:
                target_user_id = int(text)
            except ValueError:
                await update.message.reply_text(
                    "❌ <b>Invalid User ID</b>\n\n"
                    "Please enter a valid numeric user ID.",
                    parse_mode='HTML'
                )
                return

            # Check if user exists
            from services.user_service import UserService
            user_service = UserService()
            target_user = await user_service.get_user(target_user_id)

            if not target_user:
                await update.message.reply_text(
                    "❌ <b>User Not Found</b>\n\n"
                    "No user found with this ID. Please check and try again.",
                    parse_mode='HTML'
                )
                return

            # Get current ban status
            from services.admin_service import AdminService
            admin_service = AdminService()
            admin_settings = await admin_service.get_admin_settings()
            withdrawal_ban_list = admin_settings.get('withdrawal_ban_list', [])

            is_banned = target_user_id in withdrawal_ban_list

            # Toggle ban status
            if is_banned:
                withdrawal_ban_list.remove(target_user_id)
                action = "unbanned"
                status = "🟢 Enabled"
            else:
                withdrawal_ban_list.append(target_user_id)
                action = "banned"
                status = "🔴 Disabled"

            # Update settings
            await admin_service.update_admin_setting('withdrawal_ban_list', withdrawal_ban_list)

            # Clear session
            await self.clear_user_session(user_id)

            # Success message
            user_name = target_user.get('first_name', 'Unknown')
            message = f"✅ <b>User Withdrawal {action.title()}</b>\n\n"
            message += f"👤 <b>User Details:</b>\n"
            message += f"• Name: {user_name}\n"
            message += f"• User ID: {target_user_id}\n"
            message += f"• Withdrawal Status: {status}\n\n"
            message += f"📝 The user has been {action} from making withdrawals."

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manage_withdrawals')]
            ])

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_ban_user_withdrawal_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_change_user_cashout_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle change user cashout information - step 2"""
        user_id = update.effective_user.id
        text = update.message.text.strip()

        try:
            # Validate user ID
            try:
                target_user_id = int(text)
            except ValueError:
                await update.message.reply_text(
                    "❌ <b>Invalid User ID</b>\n\n"
                    "Please enter a valid numeric user ID.",
                    parse_mode='HTML'
                )
                return

            # Check if user exists
            from services.user_service import UserService
            user_service = UserService()
            target_user = await user_service.get_user(target_user_id)

            if not target_user:
                await update.message.reply_text(
                    "❌ <b>User Not Found</b>\n\n"
                    "No user found with this ID. Please check and try again.",
                    parse_mode='HTML'
                )
                return

            # Reset user's withdrawal method
            from services.withdrawal_service import WithdrawalService
            withdrawal_service = WithdrawalService()

            # Clear withdrawal method and account details
            await withdrawal_service.reset_user_withdrawal_method(target_user_id)

            # Clear session
            await self.clear_user_session(user_id)

            # Success message
            user_name = target_user.get('first_name', 'Unknown')
            message = f"✅ <b>User Cashout Information Reset</b>\n\n"
            message += f"👤 <b>User Details:</b>\n"
            message += f"• Name: {user_name}\n"
            message += f"• User ID: {target_user_id}\n\n"
            message += f"🔄 <b>Actions Completed:</b>\n"
            message += f"• Cleared withdrawal method selection\n"
            message += f"• Removed all bank/USDT account details\n"
            message += f"• Reset account setup status\n\n"
            message += f"📝 The user will need to set up their withdrawal method again when they next access their wallet."

            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Withdrawal Management', callback_data='manage_withdrawals')]
            ])

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_change_user_cashout_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== NEW USER BONUS MANAGEMENT SESSION HANDLERS ====================

    async def _handle_set_new_user_bonus_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle new user bonus range input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Validate range format (e.g., "40-51")
            if '-' not in message_text:
                await update.message.reply_text(
                    "❌ Invalid format. Please use format like: 40-51",
                    parse_mode='HTML'
                )
                return

            parts = message_text.split('-')
            if len(parts) != 2:
                await update.message.reply_text(
                    "❌ Invalid format. Please use format like: 40-51",
                    parse_mode='HTML'
                )
                return

            try:
                min_amount = int(parts[0])
                max_amount = int(parts[1])

                if min_amount <= 0 or max_amount <= 0 or min_amount >= max_amount:
                    await update.message.reply_text(
                        "❌ Invalid range. Minimum must be less than maximum and both must be positive.",
                        parse_mode='HTML'
                    )
                    return

            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid numbers. Please use format like: 40-51",
                    parse_mode='HTML'
                )
                return

            # Update admin settings
            from services.admin_service import AdminService
            admin_service = AdminService()
            success = await admin_service.update_admin_setting('joining_bonus_amount_range', message_text)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Add back button for navigation
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
                ])

                await update.message.reply_text(
                    f"✅ <b>New User Bonus Updated</b>\n\n"
                    f"New range: {message_text}",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Add back button for navigation even on failure
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
                ])

                await update.message.reply_text(
                    "❌ Failed to update new user bonus range.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_set_new_user_bonus_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_set_invite_bonus_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle invite bonus range input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Validate range format (e.g., "1-3")
            if '-' not in message_text:
                await update.message.reply_text(
                    "❌ Invalid format. Please use format like: 1-3",
                    parse_mode='HTML'
                )
                return

            parts = message_text.split('-')
            if len(parts) != 2:
                await update.message.reply_text(
                    "❌ Invalid format. Please use format like: 1-3",
                    parse_mode='HTML'
                )
                return

            try:
                min_amount = int(parts[0])
                max_amount = int(parts[1])

                if min_amount <= 0 or max_amount <= 0 or min_amount >= max_amount:
                    await update.message.reply_text(
                        "❌ Invalid range. Minimum must be less than maximum and both must be positive.",
                        parse_mode='HTML'
                    )
                    return

            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid numbers. Please use format like: 1-3",
                    parse_mode='HTML'
                )
                return

            # Update admin settings
            from services.admin_service import AdminService
            admin_service = AdminService()
            success = await admin_service.update_admin_setting('per_refer_amount_range', message_text)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Add back button for navigation
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
                ])

                await update.message.reply_text(
                    f"✅ <b>Invite Bonus Updated</b>\n\n"
                    f"New range: {message_text}",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Add back button for navigation even on failure
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
                ])

                await update.message.reply_text(
                    "❌ Failed to update invite bonus range.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_set_invite_bonus_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_configure_level_rewards_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle level rewards referral requirements input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Parse comma-separated values
            try:
                requirements = [int(x.strip()) for x in message_text.split(',')]

                if len(requirements) == 0:
                    await update.message.reply_text(
                        "❌ Please provide at least one requirement.",
                        parse_mode='HTML'
                    )
                    return

                if any(req <= 0 for req in requirements):
                    await update.message.reply_text(
                        "❌ All requirements must be positive numbers.",
                        parse_mode='HTML'
                    )
                    return

            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid format. Please use comma-separated numbers like: 1,5,10,15,20,25",
                    parse_mode='HTML'
                )
                return

            # Store requirements and ask for bonus amounts
            await self.set_user_session(user_id, 'configure_level_rewards_amounts', {'requirements': requirements})

            message = "🏆 <b>Level Rewards Configuration</b>\n\n"
            message += f"Requirements set: {', '.join(map(str, requirements))}\n\n"
            message += "Now send bonus amounts (comma-separated):\n"
            message += "Example: 2,10,15,20,25,30\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
            ])

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_configure_level_rewards_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_configure_level_rewards_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle level rewards bonus amounts input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            requirements = data.get('requirements', [])

            # Parse comma-separated values
            try:
                bonus_amounts = [int(x.strip()) for x in message_text.split(',')]

                if len(bonus_amounts) != len(requirements):
                    await update.message.reply_text(
                        f"❌ Number of bonus amounts ({len(bonus_amounts)}) must match number of requirements ({len(requirements)}).",
                        parse_mode='HTML'
                    )
                    return

                if any(amount <= 0 for amount in bonus_amounts):
                    await update.message.reply_text(
                        "❌ All bonus amounts must be positive numbers.",
                        parse_mode='HTML'
                    )
                    return

            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid format. Please use comma-separated numbers like: 2,10,15,20,25,30",
                    parse_mode='HTML'
                )
                return

            # Update level rewards configuration using dedicated service
            from services.level_rewards_service import LevelRewardsService
            level_rewards_service = LevelRewardsService()
            success = await level_rewards_service.update_level_rewards_config(requirements, bonus_amounts)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                message = "✅ <b>Level Rewards Configuration Updated</b>\n\n"
                for i, (req, bonus) in enumerate(zip(requirements, bonus_amounts), 1):
                    message += f"Level {i}: {req} referrals = ₹{bonus}\n"

                # Add back button for navigation
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                # Add back button for navigation even on failure
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Bonus', callback_data='user_bonus')]
                ])

                await update.message.reply_text(
                    "❌ Failed to update level rewards configuration.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_configure_level_rewards_step3: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== USER DETAILS & SETTINGS SESSION HANDLERS ====================

    async def _handle_user_details_get_id_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle user ID input for user details"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Validate user ID
            try:
                target_user_id = int(message_text)
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid user ID. Please send a valid number.",
                    parse_mode='HTML'
                )
                return

            # Check if user exists
            from services.user_service import UserService
            user_service = UserService()
            user_data = await user_service.get_user(target_user_id)

            if not user_data:
                await update.message.reply_text(
                    "❌ User not found in database.",
                    parse_mode='HTML'
                )
                return

            # Clear session
            await self.clear_user_session(user_id)

            # Show user details
            from handlers.admin_handlers import AdminHandlers
            admin_handlers = AdminHandlers()
            await admin_handlers.handle_show_user_details(update, context, target_user_id)

        except Exception as e:
            logger.error(f"Error in _handle_user_details_get_id_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_add_user_balance_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle add balance amount input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()
        target_user_id = data.get('target_user_id')

        try:
            # Validate amount
            try:
                amount = float(message_text)
                if amount <= 0:
                    # Add back button for navigation even on error
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                    ])

                    await update.message.reply_text(
                        "❌ Amount must be a positive number.",
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                # Add back button for navigation even on error
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    "❌ Invalid amount. Please send a valid number.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Update user balance using AdminUserService to trigger notifications
            from services.admin_user_service import AdminUserService
            admin_user_service = AdminUserService()
            result = await admin_user_service.add_user_balance(target_user_id, amount, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                # Show success message with navigation back to user details
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    f"✅ <b>Balance Added</b>\n\n"
                    f"{result['message']}\n\n"
                    f"📱 User has been notified via message.\n\n"
                    f"Click below to return to user details.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Show failure message with navigation back to user details
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    f"❌ {result['message']}",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_add_user_balance_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_remove_user_balance_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle remove balance amount input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()
        target_user_id = data.get('target_user_id')

        try:
            # Validate amount
            try:
                amount = float(message_text)
                if amount <= 0:
                    # Add back button for navigation even on error
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                    ])

                    await update.message.reply_text(
                        "❌ Amount must be a positive number.",
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                # Add back button for navigation even on error
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    "❌ Invalid amount. Please send a valid number.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Update user balance using AdminUserService to trigger notifications
            from services.admin_user_service import AdminUserService
            admin_user_service = AdminUserService()
            result = await admin_user_service.remove_user_balance(target_user_id, amount, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                # Show success message with navigation back to user details
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    f"✅ <b>Balance Removed</b>\n\n"
                    f"{result['message']}\n\n"
                    f"📱 User has been notified via message.\n\n"
                    f"Click below to return to user details.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Show failure message with navigation back to user details
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    f"❌ {result['message']}",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_remove_user_balance_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_send_user_message_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle send message to user"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()
        target_user_id = data.get('target_user_id')

        try:
            if not message_text:
                # Add back button for navigation even on error
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    "❌ Message cannot be empty.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Send message to target user
            try:
                await context.bot.send_message(
                    chat_id=target_user_id,
                    text=f"📨 <b>Message from Admin</b>\n\n{message_text}",
                    parse_mode='HTML'
                )

                # Clear session
                await self.clear_user_session(user_id)

                # Show success message with navigation back to user details
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    f"✅ <b>Message Sent</b>\n\n"
                    f"Message sent to user {target_user_id}\n\n"
                    f"Click below to return to user details.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

            except Exception as send_error:
                logger.error(f"Failed to send message to user {target_user_id}: {send_error}")

                # Show failure message with navigation back to user details
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to User Details', callback_data=f'show_user_{target_user_id}')]
                ])

                await update.message.reply_text(
                    "❌ Failed to send message. User may have blocked the bot.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_send_user_message_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_reject_task_submission_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle reject task submission reason input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()
        submission_id = data.get('submission_id')

        try:
            # Set rejection reason
            reason = None if message_text.lower() == 'skip' else message_text

            # Reject the submission
            from services.task_service import TaskService
            task_service = TaskService()

            success = await task_service.reject_task_submission(submission_id, reason or "Rejected by admin")

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with navigation back to pending submissions
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Pending Submissions', callback_data='viewPendingSubmissions')]
                ])

                await update.message.reply_text(
                    f"✅ <b>Task Submission Rejected</b>\n\n"
                    f"Submission ID: {submission_id}\n"
                    f"Reason: {reason or 'No reason provided'}\n"
                    f"User has been notified.\n\n"
                    f"Click below to return to pending submissions.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Show failure message with navigation back to pending submissions
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Pending Submissions', callback_data='viewPendingSubmissions')]
                ])

                await update.message.reply_text(
                    f"❌ <b>Failed to Reject Submission</b>\n\n"
                    f"Submission ID: {submission_id}\n"
                    f"Please try again later.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_reject_task_submission_step2: {e}")
            await update.message.reply_text("❌ Error rejecting submission. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_broadcast_media_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle broadcast media upload"""
        user_id = update.effective_user.id
        message = update.message

        try:
            # Get media data
            media_data = None

            if message.photo:
                # Get the highest resolution photo
                photo = message.photo[-1]
                media_data = {
                    'type': 'photo',
                    'file_id': photo.file_id,
                    'caption': message.caption or ''
                }
            elif message.video:
                media_data = {
                    'type': 'video',
                    'file_id': message.video.file_id,
                    'caption': message.caption or ''
                }
            elif message.document:
                media_data = {
                    'type': 'document',
                    'file_id': message.document.file_id,
                    'caption': message.caption or ''
                }
            elif message.audio:
                media_data = {
                    'type': 'audio',
                    'file_id': message.audio.file_id,
                    'caption': message.caption or ''
                }
            else:
                # Show error message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await update.message.reply_text(
                    "❌ Please send a photo, video, document, or audio file.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Save media to broadcast draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            # Get existing draft or create new one
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}
            draft['media'] = media_data

            success = await broadcast_service.save_broadcast_draft(user_id, draft)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                media_type = media_data['type'].title()
                await update.message.reply_text(
                    f"✅ <b>Media Added Successfully</b>\n\n"
                    f"📁 <b>Type:</b> {media_type}\n"
                    f"📝 <b>Caption:</b> {media_data['caption'] or 'None'}\n\n"
                    f"Click below to return to broadcast setup.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Show failure message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await update.message.reply_text(
                    "❌ <b>Failed to Save Media</b>\n\n"
                    "Please try again later.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_broadcast_media_step2: {e}")
            await update.message.reply_text("❌ Error processing media. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_broadcast_text_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle broadcast text input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            if not message_text:
                # Show error message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await update.message.reply_text(
                    "❌ Message text cannot be empty.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Save text to broadcast draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            # Get existing draft or create new one
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}
            draft['text'] = message_text

            success = await broadcast_service.save_broadcast_draft(user_id, draft)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with navigation back to broadcast (DO NOT AUTO-START)
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                # Preview text (truncated if too long)
                preview_text = message_text[:100] + "..." if len(message_text) > 100 else message_text

                await update.message.reply_text(
                    f"✅ <b>Text Added Successfully</b>\n\n"
                    f"📝 <b>Preview:</b>\n{preview_text}\n\n"
                    f"Click below to return to broadcast setup.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Show failure message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await update.message.reply_text(
                    "❌ <b>Failed to Save Text</b>\n\n"
                    "Please try again later.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_broadcast_text_step2: {e}")
            await update.message.reply_text("❌ Error processing text. Please try again.")
            await self.clear_user_session(user_id)

    async def _handle_broadcast_buttons_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle broadcast buttons configuration"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            if not message_text:
                # Show error message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await update.message.reply_text(
                    "❌ Button configuration cannot be empty.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Parse button configuration
            buttons = self._parse_button_config(message_text)

            if not buttons:
                # Show error message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await update.message.reply_text(
                    "❌ Invalid button format. Please check the format and try again.\n\n"
                    "📝 <b>Correct format:</b>\n"
                    "<code>Button Text | URL</code>",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
                return

            # Save buttons to broadcast draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            # Get existing draft or create new one
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}
            draft['buttons'] = buttons

            success = await broadcast_service.save_broadcast_draft(user_id, draft)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                # Create button preview
                button_preview = ""
                for i, button in enumerate(buttons, 1):
                    button_preview += f"{i}. {button['text']} → {button['url']}\n"

                await update.message.reply_text(
                    f"✅ <b>Buttons Added Successfully</b>\n\n"
                    f"⌨️ <b>Configured Buttons:</b>\n{button_preview}\n"
                    f"Click below to return to broadcast setup.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Show failure message with navigation back to broadcast
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='userBroadcast')]
                ])

                await update.message.reply_text(
                    "❌ <b>Failed to Save Buttons</b>\n\n"
                    "Please try again later.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_broadcast_buttons_step2: {e}")
            await update.message.reply_text("❌ Error processing buttons. Please try again.")
            await self.clear_user_session(user_id)

    # ==================== TASK MANAGEMENT SESSION HANDLERS ====================

    async def _handle_edit_task_name_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle edit task name input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()
        task_id = data.get('task_id')

        try:
            if not message_text:
                await update.message.reply_text(
                    "❌ Task name cannot be empty. Please try again.",
                    parse_mode='HTML'
                )
                return

            if len(message_text) > 100:
                await update.message.reply_text(
                    "❌ Task name too long. Maximum 100 characters allowed.",
                    parse_mode='HTML'
                )
                return

            # Update task name
            from services.task_service import TaskService
            task_service = TaskService()
            success = await task_service.update_task(task_id, {'name': message_text})

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with go back button
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Go Back', callback_data=f'manage_task_{task_id}')]
                ])

                await update.message.reply_text(
                    "✅ <b>Task Name Edited Successfully..✅️</b>",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    "❌ Failed to update task name.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_edit_task_name_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_edit_task_bonus_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle edit task bonus input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()
        task_id = data.get('task_id')

        try:
            # Validate bonus amount
            try:
                bonus_amount = float(message_text)
                if bonus_amount <= 0:
                    await update.message.reply_text(
                        "❌ Bonus amount must be a positive number.",
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid bonus amount. Please enter a valid number.",
                    parse_mode='HTML'
                )
                return

            # Update task bonus
            from services.task_service import TaskService
            task_service = TaskService()
            success = await task_service.update_task(task_id, {'reward_amount': bonus_amount})

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with go back button
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Go Back', callback_data=f'manage_task_{task_id}')]
                ])

                await update.message.reply_text(
                    "✅ <b>Task Bonus Edited Successfully..✅️</b>",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    "❌ Failed to update task bonus.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_edit_task_bonus_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_edit_task_content_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle edit task content input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()
        task_id = data.get('task_id')

        try:
            if not message_text:
                await update.message.reply_text(
                    "❌ Task content cannot be empty. Please try again.",
                    parse_mode='HTML'
                )
                return

            if len(message_text) < 10:
                await update.message.reply_text(
                    "❌ Task content too short. Minimum 10 characters required.",
                    parse_mode='HTML'
                )
                return

            # Update task content
            from services.task_service import TaskService
            task_service = TaskService()
            success = await task_service.update_task(task_id, {'description': message_text})

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with go back button
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Go Back', callback_data=f'manage_task_{task_id}')]
                ])

                await update.message.reply_text(
                    "✅ <b>Task Content Edited Successfully..✅️</b>",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    "❌ Failed to update task content.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_edit_task_content_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_edit_task_image_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle edit task image URL input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()
        task_id = data.get('task_id')

        try:
            # Basic URL validation
            if message_text and not (message_text.startswith('http://') or message_text.startswith('https://')):
                await update.message.reply_text(
                    "❌ Invalid URL format. Please provide a valid HTTP/HTTPS URL.",
                    parse_mode='HTML'
                )
                return

            # Update task image URL
            from services.task_service import TaskService
            task_service = TaskService()
            success = await task_service.update_task(task_id, {'media_url': message_text})

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with go back button
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Go Back', callback_data=f'manage_task_{task_id}')]
                ])

                await update.message.reply_text(
                    "✅ <b>Image URL Edited Successfully..✅️</b>",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    "❌ Failed to update image URL.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_edit_task_image_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_add_task_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle add task step 2 - parse name and bonus"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Parse TaskName-Amount format
            if '-' not in message_text:
                await update.message.reply_text(
                    "❌ Invalid format. Please use format: TaskName-Amount\n\nExample: Promote & Earn-5",
                    parse_mode='HTML'
                )
                return

            parts = message_text.rsplit('-', 1)  # Split from right to handle names with dashes
            if len(parts) != 2:
                await update.message.reply_text(
                    "❌ Invalid format. Please use format: TaskName-Amount\n\nExample: Promote & Earn-5",
                    parse_mode='HTML'
                )
                return

            task_name = parts[0].strip()
            try:
                bonus_amount = float(parts[1].strip())
                if bonus_amount <= 0:
                    await update.message.reply_text(
                        "❌ Bonus amount must be a positive number.",
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid bonus amount. Please enter a valid number.",
                    parse_mode='HTML'
                )
                return

            if not task_name or len(task_name) > 100:
                await update.message.reply_text(
                    "❌ Task name must be between 1-100 characters.",
                    parse_mode='HTML'
                )
                return

            # Store task data and move to next step
            task_data = {
                'name': task_name,
                'reward_amount': bonus_amount
            }

            await self.set_user_session(user_id, 'add_task_step2', task_data)

            message = "📝 <b>Add New Task - Step 2</b>\n\n"
            message += "Enter the task content (or steps to do):\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Task Management', callback_data='manage_tasks')]
            ])

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_add_task_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_add_task_step3(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle add task step 3 - get task content"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            if not message_text or len(message_text) < 10:
                await update.message.reply_text(
                    "❌ Task content must be at least 10 characters long.",
                    parse_mode='HTML'
                )
                return

            # Add content to task data
            task_data = data.copy()
            task_data['description'] = message_text

            await self.set_user_session(user_id, 'add_task_step3', task_data)

            message = "🖼️ <b>Add New Task - Step 3</b>\n\n"
            message += "Enter the demo image or video direct URL:\n\n"
            message += "Send /cancel to cancel the process."

            # Add back button for navigation
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Task Management', callback_data='manage_tasks')]
            ])

            await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_add_task_step3: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_add_task_step4(self, update: Update, context: ContextTypes.DEFAULT_TYPE, data: Dict[str, Any]) -> None:
        """Handle add task step 4 - get media URL and create task"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Basic URL validation (optional field)
            if message_text and not (message_text.startswith('http://') or message_text.startswith('https://')):
                await update.message.reply_text(
                    "❌ Invalid URL format. Please provide a valid HTTP/HTTPS URL or send /cancel to skip.",
                    parse_mode='HTML'
                )
                return

            # Create the task
            from services.task_service import TaskService
            from models.task import TaskModel

            task_service = TaskService()

            # Prepare task data
            task_data = TaskModel.create_new_task(
                name=data['name'],
                description=data['description'],
                reward_amount=data['reward_amount'],
                media_url=message_text if message_text else "",
                status="active",
                created_by=user_id
            )

            # Add task to database
            success = await task_service.add_task(task_data)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with go back button
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Go Back', callback_data='manage_tasks')]
                ])

                await update.message.reply_text(
                    "✅ <b>Task Created Successfully! ✅</b>\n\n"
                    f"Task Name: {data['name']}\n"
                    f"Bonus: ₹{data['reward_amount']}\n"
                    f"Status: Active",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Add back button for navigation even on failure
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Task Management', callback_data='manage_tasks')]
                ])

                await update.message.reply_text(
                    "❌ Failed to create task. Please try again later.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_add_task_step4: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== ADMIN MANAGEMENT SESSION HANDLERS ====================

    async def _handle_add_admin_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle add admin user ID input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Validate user ID format
            try:
                target_user_id = int(message_text)
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid user ID format. Please enter a numeric user ID.",
                    parse_mode='HTML'
                )
                return

            # Check if user ID is already an admin
            from utils.helpers import is_admin
            if is_admin(target_user_id):
                await update.message.reply_text(
                    "❌ This user is already an admin.",
                    parse_mode='HTML'
                )
                return

            # Add to admin list (store in primary admin's settings)
            from services.admin_service import AdminService
            from config.settings import settings

            admin_service = AdminService()
            # Get admin settings from PRIMARY admin (not current user)
            admin_settings = await admin_service.get_admin_settings(settings.ADMIN_ID)
            admin_list = admin_settings.get('admin_list', [])

            if target_user_id not in admin_list:
                admin_list.append(target_user_id)
                # Update PRIMARY admin's settings (not current user's)
                success = await admin_service.update_admin_setting('admin_list', admin_list, settings.ADMIN_ID)
                logger.info(f"Added admin {target_user_id} to primary admin's ({settings.ADMIN_ID}) admin list")

                # Clear session
                await self.clear_user_session(user_id)

                if success:
                    # Clear admin cache for the newly added admin
                    from utils.helpers import clear_admin_cache
                    clear_admin_cache(target_user_id)
                    logger.info(f"Cleared admin cache for newly added admin {target_user_id}")

                    # Log admin action
                    from services.admin_logging_service import AdminLoggingService
                    logging_service = AdminLoggingService()
                    await logging_service.log_admin_added(user_id, target_user_id)

                    # Show success message with go back button
                    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Go Back', callback_data='manage_admins')]
                    ])

                    await update.message.reply_text(
                        "✅ <b>Promoted As Admin</b>",
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                else:
                    # Add back button for navigation even on failure
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Admin Management', callback_data='manage_admins')]
                    ])

                    await update.message.reply_text(
                        "❌ Failed to add admin.",
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
            else:
                # Add back button for navigation
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Admin Management', callback_data='manage_admins')]
                ])

                await update.message.reply_text(
                    "❌ This user is already in the admin list.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_add_admin_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_remove_admin_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle remove admin user ID input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Validate user ID format
            try:
                target_user_id = int(message_text)
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid user ID format. Please enter a numeric user ID.",
                    parse_mode='HTML'
                )
                return

            # Check if user ID is in admin list
            from services.admin_service import AdminService
            admin_service = AdminService()
            admin_settings = await admin_service.get_admin_settings()
            admin_list = admin_settings.get('admin_list', [])

            if target_user_id in admin_list:
                admin_list.remove(target_user_id)
                success = await admin_service.update_admin_setting('admin_list', admin_list)

                # Clear session
                await self.clear_user_session(user_id)

                if success:
                    # Log admin action
                    from services.admin_logging_service import AdminLoggingService
                    logging_service = AdminLoggingService()
                    await logging_service.log_admin_removed(user_id, target_user_id)

                    # Show success message with go back button
                    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Go Back', callback_data='manage_admins')]
                    ])

                    await update.message.reply_text(
                        "✅ <b>Removed Admin Rights</b>",
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
                else:
                    # Add back button for navigation even on failure
                    keyboard = InlineKeyboardMarkup([
                        [InlineKeyboardButton('↩️ Back to Admin Management', callback_data='manage_admins')]
                    ])

                    await update.message.reply_text(
                        "❌ Failed to remove admin.",
                        reply_markup=keyboard,
                        parse_mode='HTML'
                    )
            else:
                # Add back button for navigation
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Admin Management', callback_data='manage_admins')]
                ])

                await update.message.reply_text(
                    "❌ This user is not in the admin list.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_remove_admin_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== BROADCAST SESSION HANDLERS ====================

    async def _handle_broadcast_media_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle broadcast media input"""
        user_id = update.effective_user.id

        logger.info(f"Processing broadcast media upload for user {user_id}")

        try:
            # Check if message has media
            media_data = None

            logger.debug(f"Checking media types for user {user_id}")
            logger.debug(f"Message has photo: {bool(update.message.photo)}")
            logger.debug(f"Message has video: {bool(update.message.video)}")
            logger.debug(f"Message has document: {bool(update.message.document)}")

            if update.message.photo:
                media_data = {
                    'type': 'photo',
                    'file_id': update.message.photo[-1].file_id
                }
            elif update.message.video:
                media_data = {
                    'type': 'video',
                    'file_id': update.message.video.file_id
                }
            elif update.message.document:
                media_data = {
                    'type': 'document',
                    'file_id': update.message.document.file_id
                }
            elif update.message.audio:
                media_data = {
                    'type': 'audio',
                    'file_id': update.message.audio.file_id
                }
            elif update.message.voice:
                media_data = {
                    'type': 'voice',
                    'file_id': update.message.voice.file_id
                }
            elif update.message.sticker:
                media_data = {
                    'type': 'sticker',
                    'file_id': update.message.sticker.file_id
                }
            elif update.message.animation:
                media_data = {
                    'type': 'animation',
                    'file_id': update.message.animation.file_id
                }
            elif update.message.video_note:
                media_data = {
                    'type': 'video_note',
                    'file_id': update.message.video_note.file_id
                }
            else:
                logger.warning(f"No valid media detected for user {user_id}")
                await update.message.reply_text(
                    "❌ Please send a valid media file (photo, video, document, audio, voice, sticker, GIF, or round video).",
                    parse_mode='HTML'
                )
                return

            logger.info(f"Media detected for user {user_id}: {media_data}")

            # Save media to broadcast draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            logger.info(f"Getting existing broadcast draft for user {user_id}")
            # Get existing draft or create new one
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}
            draft['media'] = media_data

            logger.info(f"Saving broadcast draft for user {user_id}: {draft}")
            success = await broadcast_service.save_broadcast_draft(user_id, draft)
            logger.info(f"Broadcast draft save result for user {user_id}: {success}")

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with next button
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➡️ Next', callback_data='user_broadcast')]
                ])

                await update.message.reply_text(
                    "✅ <b>Message successfully modified.</b>",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Add back button for navigation even on failure
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                ])

                await update.message.reply_text(
                    "❌ Failed to save media.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_broadcast_media_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_broadcast_text_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle broadcast text input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            if not message_text:
                await update.message.reply_text(
                    "❌ Message text cannot be empty. Please try again.",
                    parse_mode='HTML'
                )
                return

            # Save text to broadcast draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            # Get existing draft or create new one
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}
            draft['text'] = message_text

            success = await broadcast_service.save_broadcast_draft(user_id, draft)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with next button
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➡️ Next', callback_data='user_broadcast')]
                ])

                await update.message.reply_text(
                    "✅ <b>Message successfully modified.</b>",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Add back button for navigation even on failure
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Broadcast', callback_data='user_broadcast')]
                ])

                await update.message.reply_text(
                    "❌ Failed to save text.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_broadcast_text_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_broadcast_buttons_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle broadcast buttons input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            if not message_text:
                await update.message.reply_text(
                    "❌ Button configuration cannot be empty. Please try again.",
                    parse_mode='HTML'
                )
                return

            # Parse button configuration
            buttons = self._parse_button_config(message_text)

            if not buttons:
                await update.message.reply_text(
                    "❌ Invalid button format. Please check the format and try again.",
                    parse_mode='HTML'
                )
                return

            # Save buttons to broadcast draft
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()

            # Get existing draft or create new one
            draft = await broadcast_service.get_broadcast_draft(user_id) or {}
            draft['buttons'] = buttons

            success = await broadcast_service.save_broadcast_draft(user_id, draft)

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with back and next buttons
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton('🔙 Back', callback_data='user_broadcast'),
                        InlineKeyboardButton('➡️ Next', callback_data='user_broadcast')
                    ]
                ])

                await update.message.reply_text(
                    "✅ <b>Message successfully modified.</b>",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                await update.message.reply_text(
                    "❌ Failed to save buttons.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_broadcast_buttons_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    def _parse_button_config(self, config_text: str) -> List[List[Dict[str, str]]]:
        """Parse button configuration text into button structure"""
        try:
            buttons = []
            lines = config_text.strip().split('\n')

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Check if line contains multiple buttons (&&)
                if '&&' in line:
                    # Multiple buttons in one row
                    button_row = []
                    button_parts = line.split('&&')

                    for button_part in button_parts:
                        button_part = button_part.strip()
                        if ' - ' in button_part:
                            text, action = button_part.split(' - ', 1)
                            button_row.append({
                                'text': text.strip(),
                                'action': action.strip()
                            })

                    if button_row:
                        buttons.append(button_row)
                else:
                    # Single button in row
                    if ' - ' in line:
                        text, action = line.split(' - ', 1)
                        buttons.append([{
                            'text': text.strip(),
                            'action': action.strip()
                        }])

            return buttons

        except Exception as e:
            logger.error(f"Error parsing button config: {e}")
            return []

    # ==================== GIFT CODE SESSION HANDLERS ====================

    async def _handle_add_redeem_code_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle add redeem code input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Parse format: min-max,totalUsers,code
            parts = message_text.split(',')
            if len(parts) != 3:
                await update.message.reply_text(
                    "❌ Invalid format. Please use: min-max,totalUsers,code\n\nExample: 10-50,80,PROMO",
                    parse_mode='HTML'
                )
                return

            # Parse amount range
            amount_range = parts[0].strip()
            if '-' not in amount_range:
                await update.message.reply_text(
                    "❌ Invalid amount range. Please use format: min-max\n\nExample: 10-50",
                    parse_mode='HTML'
                )
                return

            try:
                min_amount, max_amount = amount_range.split('-')
                min_amount = float(min_amount.strip())
                max_amount = float(max_amount.strip())

                if min_amount <= 0 or max_amount <= 0 or min_amount > max_amount:
                    await update.message.reply_text(
                        "❌ Invalid amount range. Min and max must be positive, and min must be less than max.",
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid amount values. Please enter valid numbers.",
                    parse_mode='HTML'
                )
                return

            # Parse total users
            try:
                total_users = int(parts[1].strip())
                if total_users <= 0:
                    await update.message.reply_text(
                        "❌ Total users must be a positive number.",
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid total users value. Please enter a valid number.",
                    parse_mode='HTML'
                )
                return

            # Parse code
            code = parts[2].strip().upper()
            if not code or len(code) < 3:
                await update.message.reply_text(
                    "❌ Code must be at least 3 characters long.",
                    parse_mode='HTML'
                )
                return

            # Create gift code
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()
            success = await gift_service.create_admin_gift_code(
                code=code,
                min_amount=min_amount,
                max_amount=max_amount,
                usage_limit=total_users,
                created_by=user_id
            )

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with options
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                message = "✅ <b>Gift Code Created Successfully!</b>\n\n"
                message += f"📝 Code: {code}\n"
                message += f"💰 Amount: ₹{min_amount}-₹{max_amount}\n"
                message += f"🔢 Usage Limit: {total_users}\n\n"
                message += "Users can now redeem this code in the Extra Rewards section!"

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➕ Generate Another Code', callback_data='add_redeem_code')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')]
                ])

                await update.message.reply_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Add back button for navigation even on failure
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('↩️ Back to Gift Code Management', callback_data='manage_gift_codes')]
                ])

                await update.message.reply_text(
                    "❌ Failed to create gift code. Code might already exist.",
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_add_redeem_code_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_link_fixed_amount_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle link fixed amount input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Validate amount
            try:
                amount = float(message_text)
                if amount <= 0:
                    await update.message.reply_text(
                        "❌ Amount must be a positive number.",
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid amount. Please enter a valid number.",
                    parse_mode='HTML'
                )
                return

            # Update session with fixed amount settings (preserving existing data)
            await self.update_user_session_data(user_id, 'link_redeem_config', {
                'amount_type': 'fixed',
                'amount_value': amount
            })



            # Show success message with go back button
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Go Back', callback_data='link_based_redeem')]
            ])

            await update.message.reply_text(
                f"✅ <b>Fixed amount set to ₹{amount} per user</b>",
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in _handle_link_fixed_amount_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_link_random_amount_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle link random amount input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Parse range format: min-max
            if '-' not in message_text:
                await update.message.reply_text(
                    "❌ Invalid format. Please use: MIN-MAX\n\nExample: 1-10",
                    parse_mode='HTML'
                )
                return

            try:
                parts = message_text.split('-')
                if len(parts) != 2:
                    await update.message.reply_text(
                        "❌ Invalid format. Please use: MIN-MAX\n\nExample: 1-10",
                        parse_mode='HTML'
                    )
                    return

                min_amount = float(parts[0].strip())
                max_amount = float(parts[1].strip())

                if min_amount <= 0 or max_amount <= 0:
                    await update.message.reply_text(
                        "❌ Both minimum and maximum amounts must be positive numbers.",
                        parse_mode='HTML'
                    )
                    return

                if min_amount >= max_amount:
                    await update.message.reply_text(
                        "❌ Minimum amount must be less than maximum amount.",
                        parse_mode='HTML'
                    )
                    return

            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid amount values. Please enter valid numbers.\n\nExample: 1-10",
                    parse_mode='HTML'
                )
                return

            # Update session with random amount settings (preserving existing data)
            await self.update_user_session_data(user_id, 'link_redeem_config', {
                'amount_type': 'random',
                'amount_value': {'min': min_amount, 'max': max_amount}
            })



            # Show success message with go back button
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Go Back', callback_data='link_based_redeem')]
            ])

            await update.message.reply_text(
                f"✅ <b>Random amount set to ₹{min_amount}–₹{max_amount} range</b>",
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in _handle_link_random_amount_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_link_user_limit_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle link user limit input"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Parse user limit (case-insensitive for 'unlimited')
            if message_text.lower() == 'unlimited':
                user_limit = -1
                limit_text = "unlimited"
            else:
                try:
                    user_limit = int(message_text)
                    if user_limit <= 0:
                        await update.message.reply_text(
                            "❌ User limit must be a positive number or 'unlimited'.\n\nExample: 100",
                            parse_mode='HTML'
                        )
                        return
                    limit_text = f"{user_limit} users"
                except ValueError:
                    await update.message.reply_text(
                        "❌ Invalid input. Please enter a positive number or 'unlimited'.\n\nExample: 100",
                        parse_mode='HTML'
                    )
                    return

            # Update session with user limit settings (preserving existing data)
            await self.update_user_session_data(user_id, 'link_redeem_config', {
                'user_limit': user_limit
            })



            # Show success message with go back button
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Go Back', callback_data='link_based_redeem')]
            ])

            await update.message.reply_text(
                f"✅ <b>User limit set to {limit_text}</b>",
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in _handle_link_user_limit_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== FORCESUB SESSION HANDLERS ====================

    async def _handle_set_main_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle set main channel input"""
        user_id = update.effective_user.id
        channel_input = update.message.text.strip()

        try:
            # Set main channel using ForceSubscriptionService
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService()

            success, message = await force_service.set_main_channel(channel_input, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            # Show result with navigation buttons
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            if success:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🏡 Set Another Channel', callback_data='set_main_channel')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='forcesub_channels')]
                ])
            else:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='set_main_channel')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='forcesub_channels')]
                ])

            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in _handle_set_main_channel_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_add_forcesub_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle add forcesub channel forwarded message"""
        user_id = update.effective_user.id

        try:
            # Check if message is forwarded from a channel
            if not update.message.forward_from_chat:
                await update.message.reply_text(
                    "❌ <b>Please forward a message from the channel</b>\n\n"
                    "To add a channel:\n"
                    "1️⃣ Go to the channel you want to add\n"
                    "2️⃣ Forward any message from that channel to this bot\n"
                    "3️⃣ I will automatically verify and add it\n\n"
                    "Send /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            forwarded_chat = update.message.forward_from_chat

            # Check if it's a channel (not a group or private chat)
            if forwarded_chat.type != 'channel':
                await update.message.reply_text(
                    "❌ <b>The forwarded message must be from a channel</b>\n\n"
                    "Please forward a message from a <b>channel</b>, not a group or private chat.\n\n"
                    "Send /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            # Extract channel information
            channel_id = forwarded_chat.id
            channel_title = forwarded_chat.title
            channel_username = getattr(forwarded_chat, 'username', None)

            # Show processing message
            processing_msg = await update.message.reply_text(
                "🔄 <b>Processing channel...</b>\n\n"
                f"📢 <b>Channel:</b> {channel_title}\n"
                f"🆔 <b>ID:</b> {channel_id}\n"
                f"📱 <b>Type:</b> {'Public' if channel_username else 'Private'}\n\n"
                "⏳ Verifying bot permissions...",
                parse_mode='HTML'
            )

            # Add channel using ForceSubscriptionService with forwarded message data
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService()

            success, message = await force_service.add_channel_from_forwarded_message(
                channel_id, channel_title, channel_username, user_id
            )

            # Clear session
            await self.clear_user_session(user_id)

            # Show result with navigation buttons
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            if success:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➕ Add Another Channel', callback_data='add_forcesub_channel')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='forcesub_channels')]
                ])
            else:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='add_forcesub_channel')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='forcesub_channels')]
                ])

            # Edit the processing message with the final result
            try:
                await processing_msg.edit_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            except Exception:
                # If editing fails, send a new message
                await update.message.reply_text(
                    message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_add_forcesub_channel_step2: {e}")

            error_message = (
                "❌ <b>Error</b>\n\n"
                "Something went wrong while processing the channel.\n"
                "Please try again later or contact support if the issue persists."
            )

            # Try to edit the processing message if it exists
            try:
                if 'processing_msg' in locals():
                    await processing_msg.edit_text(error_message, parse_mode='HTML')
                else:
                    await update.message.reply_text(error_message, parse_mode='HTML')
            except Exception:
                await update.message.reply_text(error_message, parse_mode='HTML')

            await self.clear_user_session(user_id)

    async def _handle_remove_forcesub_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle remove forcesub channel input"""
        user_id = update.effective_user.id
        channel_number_input = update.message.text.strip()

        try:
            # Validate channel number
            try:
                channel_number = int(channel_number_input)
                if channel_number <= 0:
                    await update.message.reply_text(
                        "❌ Please enter a valid channel number.",
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                await update.message.reply_text(
                    "❌ Please enter a valid number.",
                    parse_mode='HTML'
                )
                return

            # Remove channel using ForceSubscriptionService
            from services.force_subscription_service import ForceSubscriptionService
            force_service = ForceSubscriptionService()

            success, message = await force_service.remove_channel_by_number(channel_number, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            # Show result with navigation buttons
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            if success:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('➖ Remove Another Channel', callback_data='remove_forcesub_channel')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='forcesub_channels')]
                ])
            else:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='remove_forcesub_channel')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='forcesub_channels')]
                ])

            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in _handle_remove_forcesub_channel_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== GIFT BROADCAST SESSION HANDLERS ====================

    async def _handle_gift_broadcast_forward_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle gift broadcast forwarded message step 2 - NEW IMPROVED METHOD"""
        user_id = update.effective_user.id
        message = update.message

        try:
            # Check if message is forwarded
            if not message.forward_from_chat:
                await update.message.reply_text(
                    "❌ <b>Not a Forwarded Message</b>\n\n"
                    "Please forward a message from your target channel.\n\n"
                    "💡 <b>How to forward:</b>\n"
                    "1. Go to your channel\n"
                    "2. Select any message\n"
                    "3. Tap 'Forward'\n"
                    "4. Send it to this bot\n\n"
                    "Send /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            # Extract channel information from forwarded message
            forward_from_chat = message.forward_from_chat
            channel_id = forward_from_chat.id
            channel_title = forward_from_chat.title
            channel_username = getattr(forward_from_chat, 'username', None)
            channel_type = forward_from_chat.type

            # Validate it's a channel
            if channel_type not in ['channel', 'supergroup']:
                await update.message.reply_text(
                    "❌ <b>Invalid Source</b>\n\n"
                    "The forwarded message must be from a channel or supergroup.\n\n"
                    "Please forward a message from your target channel.\n\n"
                    "Send /cancel to cancel the process.",
                    parse_mode='HTML'
                )
                return

            # Use the bot instance from context (already initialized)
            bot = context.bot

            try:
                # Get chat information
                chat_info = await bot.get_chat(channel_id)

                # Check if bot is admin
                bot_member = await bot.get_chat_member(channel_id, bot.id)
                if bot_member.status not in ['administrator', 'creator']:
                    await update.message.reply_text(
                        f"❌ <b>Bot Not Admin</b>\n\n"
                        f"📢 Channel: {channel_title}\n"
                        f"🆔 ID: {channel_id}\n\n"
                        f"The bot must be an administrator in this channel to create gift broadcasts.\n\n"
                        f"Please add the bot as an admin and try again.\n\n"
                        f"Send /cancel to cancel the process.",
                        parse_mode='HTML'
                    )
                    return

                # Get member count if possible
                try:
                    member_count = await bot.get_chat_member_count(channel_id)
                except Exception:
                    member_count = "Unknown"

                # Create channel info
                channel_info = {
                    'id': channel_id,
                    'title': channel_title,
                    'username': channel_username,
                    'type': 'public' if channel_username else 'private',
                    'member_count': member_count,
                    'invite_link': None
                }

                # For private channels, try to get invite link
                if not channel_username:
                    try:
                        invite_link = await bot.export_chat_invite_link(channel_id)
                        channel_info['invite_link'] = invite_link
                    except Exception as e:
                        logger.warning(f"Could not get invite link for {channel_id}: {e}")

                # Store channel info in session
                session_data = {
                    'channel_info': channel_info,
                    'channel_type': channel_info['type'],
                    'needs_invite_link': False  # We already have it or don't need it
                }

                # Show success and proceed to reward amount
                message_text = f"✅ <b>Channel Detected Successfully!</b>\n\n"
                message_text += f"📢 <b>Channel:</b> {channel_title}\n"
                message_text += f"🆔 <b>ID:</b> {channel_id}\n"
                if channel_username:
                    message_text += f"🔗 <b>Username:</b> @{channel_username}\n"
                message_text += f"🏷️ <b>Type:</b> {'Public' if channel_username else 'Private'}\n"
                message_text += f"👥 <b>Members:</b> {member_count}\n"
                if channel_info.get('invite_link'):
                    message_text += f"🔗 <b>Invite Link:</b> Available\n"
                message_text += f"\n💰 <b>Please enter the reward amount (in ₹):</b>\n\n"
                message_text += f"Example: 5 (for ₹5 per user)\n\n"
                message_text += f"Send /cancel to cancel the process."

                await update.message.reply_text(message_text, parse_mode='HTML')

                # Update session for reward step
                await self.set_user_session(user_id, 'gift_broadcast_reward', session_data)

            except Exception as e:
                logger.error(f"Error accessing channel {channel_id}: {e}")
                await update.message.reply_text(
                    f"❌ <b>Channel Access Error</b>\n\n"
                    f"📢 Channel: {channel_title}\n"
                    f"🆔 ID: {channel_id}\n\n"
                    f"Could not access channel information. Please ensure:\n"
                    f"✅ The bot is added as an administrator\n"
                    f"✅ The channel exists and is accessible\n"
                    f"✅ You have permission to manage the channel\n\n"
                    f"Send /cancel to cancel the process.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_gift_broadcast_forward_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong while processing the forwarded message. Please try again later.\n\n"
                "Send /cancel to cancel the process.",
                parse_mode='HTML'
            )

    async def _handle_gift_broadcast_channel_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle gift broadcast channel input"""
        user_id = update.effective_user.id
        channel_input = update.message.text.strip()

        try:
            # Validate channel input using GiftBroadcastService with bot instance
            from services.gift_broadcast_service import GiftBroadcastService
            gift_service = GiftBroadcastService(bot=context.bot)

            validation_result = await gift_service.validate_channel_input(channel_input)

            if not validation_result['valid']:
                await update.message.reply_text(
                    f"❌ {validation_result['error']}",
                    parse_mode='HTML'
                )
                return

            # Verify channel access
            channel_id = validation_result['channel_id']
            access_valid, access_message, channel_info = await gift_service.verify_channel_access(channel_id)

            if not access_valid:
                await update.message.reply_text(
                    f"❌ {access_message}",
                    parse_mode='HTML'
                )
                return

            # Store channel info in session
            session_data = {
                'channel_info': channel_info,
                'channel_type': validation_result['type'],
                'needs_invite_link': validation_result['needs_invite_link']
            }

            # Check if invite link is needed
            if validation_result['needs_invite_link']:
                # Ask for invite link
                message = f"✅ <b>Channel Verified!</b>\n\n"
                message += f"📢 Channel: {channel_info['title']}\n"
                message += f"🆔 ID: {channel_info['id']}\n\n"
                message += f"🔗 <b>Please send the invite link for this private channel:</b>\n\n"
                message += f"Example: https://t.me/+AbCdEfGhIjKlMnOp\n\n"
                message += f"Send /cancel to cancel the process."

                await update.message.reply_text(message, parse_mode='HTML')

                # Update session for invite link step
                await self.set_user_session(user_id, 'gift_broadcast_invite_link', session_data)
            else:
                # Public channel, proceed to reward amount
                message = f"✅ <b>Channel Verified!</b>\n\n"
                message += f"📢 Channel: {channel_info['title']}\n"
                message += f"🆔 ID: {channel_info['id']}\n"
                message += f"👥 Members: {channel_info.get('member_count', 'Unknown')}\n\n"
                message += f"💰 <b>Please enter the reward amount (in ₹):</b>\n\n"
                message += f"Example: 5 (for ₹5 per user)\n\n"
                message += f"Send /cancel to cancel the process."

                await update.message.reply_text(message, parse_mode='HTML')

                # Update session for reward step
                await self.set_user_session(user_id, 'gift_broadcast_reward', session_data)

        except Exception as e:
            logger.error(f"Error in _handle_gift_broadcast_channel_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong while validating channel. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_gift_broadcast_invite_link_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle gift broadcast invite link input"""
        user_id = update.effective_user.id
        invite_link = update.message.text.strip()

        try:
            # Validate invite link format
            if not invite_link.startswith('https://t.me/+') and not invite_link.startswith('https://t.me/joinchat/'):
                await update.message.reply_text(
                    "❌ Invalid invite link format. Please use a valid Telegram invite link.\n\nExample: https://t.me/+AbCdEfGhIjKlMnOp",
                    parse_mode='HTML'
                )
                return

            # Get session data
            session_data = await self.get_user_session(user_id)
            if not session_data or 'channel_info' not in session_data:
                await update.message.reply_text(
                    "❌ Session expired. Please start over.",
                    parse_mode='HTML'
                )
                return

            # Update session with invite link
            session_data['invite_link'] = invite_link

            channel_info = session_data['channel_info']

            message = f"✅ <b>Invite Link Added!</b>\n\n"
            message += f"📢 Channel: {channel_info['title']}\n"
            message += f"🆔 ID: {channel_info['id']}\n"
            message += f"🔗 Invite Link: {invite_link}\n\n"
            message += f"💰 <b>Please enter the reward amount (in ₹):</b>\n\n"
            message += f"Example: 5 (for ₹5 per user)\n\n"
            message += f"Send /cancel to cancel the process."

            await update.message.reply_text(message, parse_mode='HTML')

            # Update session for reward step
            await self.set_user_session(user_id, 'gift_broadcast_reward', session_data)

        except Exception as e:
            logger.error(f"Error in _handle_gift_broadcast_invite_link_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong while processing invite link. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_gift_broadcast_reward_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle gift broadcast reward amount input"""
        user_id = update.effective_user.id
        reward_input = update.message.text.strip()

        try:
            # Validate reward amount
            try:
                reward_amount = float(reward_input)
                if reward_amount <= 0:
                    await update.message.reply_text(
                        "❌ Reward amount must be a positive number.",
                        parse_mode='HTML'
                    )
                    return
                if reward_amount > 1000:  # Set reasonable limit
                    await update.message.reply_text(
                        "❌ Reward amount too high. Maximum allowed is ₹1000 per user.",
                        parse_mode='HTML'
                    )
                    return
            except ValueError:
                await update.message.reply_text(
                    "❌ Invalid amount. Please enter a valid number.",
                    parse_mode='HTML'
                )
                return

            # Get session data
            session_data = await self.get_user_session(user_id)
            if not session_data or 'channel_info' not in session_data:
                await update.message.reply_text(
                    "❌ Session expired. Please start over.",
                    parse_mode='HTML'
                )
                return

            # Create gift broadcast with bot instance
            from services.gift_broadcast_service import GiftBroadcastService
            gift_service = GiftBroadcastService(bot=context.bot)

            channel_info = session_data['channel_info']
            invite_link = session_data.get('invite_link')

            success, message, broadcast_id = await gift_service.create_gift_broadcast(
                channel_info=channel_info,
                invite_link=invite_link,
                reward_amount=reward_amount,
                admin_user_id=user_id
            )

            # Clear session
            await self.clear_user_session(user_id)

            if success:
                # Show success message with broadcast details
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                success_message = f"✅ <b>Gift Broadcast Created Successfully!</b>\n\n"
                success_message += f"📢 Channel: {channel_info['title']}\n"
                success_message += f"💰 Reward: ₹{reward_amount} per user\n"
                success_message += f"🆔 Broadcast ID: {broadcast_id}\n\n"
                success_message += f"The gift broadcast is now active! Users will receive the broadcast message and can join the channel to claim their reward."

                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🎁 Create Another Broadcast', callback_data='gift_broadcast')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='admin_panel')]
                ])

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

                # TODO: Trigger actual broadcast to all users
                # This would integrate with the existing broadcast system

            else:
                await update.message.reply_text(
                    f"❌ {message}",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_gift_broadcast_reward_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong while creating gift broadcast. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== MAINTENANCE & CUSTOM REFERRAL SESSION HANDLERS ====================

    async def _handle_delete_invite_link_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle delete invite link input"""
        user_id = update.effective_user.id
        input_text = update.message.text.strip()

        try:
            # Try to determine if input is a user ID (numeric) or parameter (text)
            from services.custom_referral_service import CustomReferralService
            referral_service = CustomReferralService()

            success = False
            message = ""

            # Check if input is numeric (user ID)
            if input_text.isdigit():
                user_id_to_delete = int(input_text)
                success, message = await referral_service.delete_custom_link_by_user_id(user_id_to_delete, user_id)
            else:
                # Treat as parameter
                success, message = await referral_service.delete_custom_link_by_parameter(input_text, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            # Show result with navigation buttons
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            if success:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🗑 Delete Another Link', callback_data='delete_invite_link')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='custom_referral_link')]
                ])
            else:
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='delete_invite_link')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='custom_referral_link')]
                ])

            await update.message.reply_text(
                message,
                reply_markup=keyboard,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error in _handle_delete_invite_link_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong while deleting invite link. Please try again later.",
                parse_mode='HTML'
            )

    # ==================== NEW STREAMLINED LINK REDEEM WORKFLOW HANDLERS ====================

    async def _handle_link_fixed_amount_new_step1(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle fixed amount input - Step 1 of new workflow"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Validate amount
            try:
                amount = float(message_text)
                if amount <= 0:
                    raise ValueError("Amount must be positive")
                if amount > 10000:
                    raise ValueError("Amount too large")
            except ValueError:
                await update.message.reply_text(
                    "❌ <b>Invalid Amount</b>\n\n"
                    "Please enter a valid positive number.\n\n"
                    "💡 <b>Example:</b> 50 (for ₹50 per user)\n\n"
                    "Send /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            # Store amount and move to step 2 (user limit)
            await self.update_user_session_data(user_id, 'link_fixed_amount_step2', {
                'amount_type': 'fixed',
                'amount_value': amount
            })

            message = "👥 <b>Fixed Amount Link</b>\n\n"
            message += f"📝 <b>Step 2 of 3:</b> Set user limit\n\n"
            message += f"💰 <b>Amount per user:</b> ₹{amount}\n\n"
            message += "How many users can claim this link?\n\n"
            message += "💡 <b>Examples:</b>\n"
            message += "• 100 (for 100 users)\n"
            message += "• 500 (for 500 users)\n"
            message += "• -1 (for unlimited users)\n\n"
            message += "👥 <b>Enter the user limit:</b>"

            await update.message.reply_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_link_fixed_amount_new_step1: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
            await self.clear_user_session(user_id)

    async def _handle_link_fixed_amount_new_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle user limit input and generate link - Step 2 of new workflow"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Get session data
            session = await self.get_user_session(user_id)
            if not session or not session.get('data'):
                await update.message.reply_text(
                    "❌ Session expired. Please start again.",
                    parse_mode='HTML'
                )
                await self.clear_user_session(user_id)
                return

            session_data = session.get('data', {})
            amount = session_data.get('amount_value')

            # Validate user limit
            try:
                if message_text == '-1':
                    user_limit = -1
                else:
                    user_limit = int(message_text)
                    if user_limit <= 0:
                        raise ValueError("User limit must be positive or -1 for unlimited")
                    if user_limit > 100000:
                        raise ValueError("User limit too large")
            except ValueError:
                await update.message.reply_text(
                    "❌ <b>Invalid User Limit</b>\n\n"
                    "Please enter a valid number or -1 for unlimited.\n\n"
                    "💡 <b>Examples:</b>\n"
                    "• 100 (for 100 users)\n"
                    "• -1 (for unlimited users)\n\n"
                    "Send /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            # Generate the redemption link
            await self._generate_redemption_link(update, user_id, {
                'amount_type': 'fixed',
                'amount_value': amount,
                'user_limit': user_limit
            })

        except Exception as e:
            logger.error(f"Error in _handle_link_fixed_amount_new_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
            await self.clear_user_session(user_id)

    async def _handle_link_random_amount_new_step1(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle random amount range input - Step 1 of new workflow"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Validate range format
            if '-' not in message_text:
                await update.message.reply_text(
                    "❌ <b>Invalid Format</b>\n\n"
                    "Please use the format: MIN-MAX\n\n"
                    "💡 <b>Example:</b> 10-100 (for ₹10 to ₹100 range)\n\n"
                    "Send /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            try:
                min_str, max_str = message_text.split('-', 1)
                min_amount = float(min_str.strip())
                max_amount = float(max_str.strip())

                if min_amount <= 0 or max_amount <= 0:
                    raise ValueError("Amounts must be positive")
                if min_amount >= max_amount:
                    raise ValueError("Min amount must be less than max amount")
                if max_amount > 10000:
                    raise ValueError("Max amount too large")
            except ValueError as e:
                await update.message.reply_text(
                    "❌ <b>Invalid Range</b>\n\n"
                    "Please enter valid positive numbers with min < max.\n\n"
                    "💡 <b>Example:</b> 10-100 (for ₹10 to ₹100 range)\n\n"
                    "Send /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            # Store range and move to step 2 (user limit)
            await self.update_user_session_data(user_id, 'link_random_amount_step2', {
                'amount_type': 'random',
                'amount_value': {'min': min_amount, 'max': max_amount}
            })

            message = "👥 <b>Random Amount Link</b>\n\n"
            message += f"📝 <b>Step 2 of 3:</b> Set user limit\n\n"
            message += f"🎲 <b>Amount range:</b> ₹{min_amount} - ₹{max_amount}\n\n"
            message += "How many users can claim this link?\n\n"
            message += "💡 <b>Examples:</b>\n"
            message += "• 100 (for 100 users)\n"
            message += "• 500 (for 500 users)\n"
            message += "• -1 (for unlimited users)\n\n"
            message += "👥 <b>Enter the user limit:</b>"

            await update.message.reply_text(message, parse_mode='HTML')

        except Exception as e:
            logger.error(f"Error in _handle_link_random_amount_new_step1: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
            await self.clear_user_session(user_id)

    async def _handle_link_random_amount_new_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle user limit input and generate link - Step 2 of random amount workflow"""
        user_id = update.effective_user.id
        message_text = update.message.text.strip()

        try:
            # Get session data
            session = await self.get_user_session(user_id)
            if not session or not session.get('data'):
                await update.message.reply_text(
                    "❌ Session expired. Please start again.",
                    parse_mode='HTML'
                )
                await self.clear_user_session(user_id)
                return

            session_data = session.get('data', {})
            amount_value = session_data.get('amount_value')

            # Validate user limit
            try:
                if message_text == '-1':
                    user_limit = -1
                else:
                    user_limit = int(message_text)
                    if user_limit <= 0:
                        raise ValueError("User limit must be positive or -1 for unlimited")
                    if user_limit > 100000:
                        raise ValueError("User limit too large")
            except ValueError:
                await update.message.reply_text(
                    "❌ <b>Invalid User Limit</b>\n\n"
                    "Please enter a valid number or -1 for unlimited.\n\n"
                    "💡 <b>Examples:</b>\n"
                    "• 100 (for 100 users)\n"
                    "• -1 (for unlimited users)\n\n"
                    "Send /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            # Generate the redemption link
            await self._generate_redemption_link(update, user_id, {
                'amount_type': 'random',
                'amount_value': amount_value,
                'user_limit': user_limit
            })

        except Exception as e:
            logger.error(f"Error in _handle_link_random_amount_new_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong. Please try again later.",
                parse_mode='HTML'
            )
            await self.clear_user_session(user_id)

    async def _generate_redemption_link(self, update: Update, user_id: int, link_data: Dict[str, Any]) -> None:
        """Generate and display redemption link - Final step of streamlined workflow"""
        try:
            # Clear session first
            await self.clear_user_session(user_id)

            # Get bot username
            from config.settings import settings
            bot_username = settings.BOT_USERNAME

            # Save to database using the correct service
            from services.gift_code_service import GiftCodeService
            gift_service = GiftCodeService()

            # Generate unique code using the service method
            code = await gift_service.generate_link_code()
            if not code:
                await update.message.reply_text(
                    "❌ <b>Error</b>\n\nFailed to generate unique code. Please try again.",
                    parse_mode='HTML'
                )
                return

            # Create link-based code in database
            success = await gift_service.create_link_based_code(
                code=code,
                amount_type=link_data['amount_type'],
                amount_value=link_data['amount_value'],
                user_limit=link_data['user_limit'],
                created_by=user_id
            )

            if success:
                # Build success message
                message = "✅ <b>Redemption Link Generated!</b>\n\n"

                if link_data['amount_type'] == 'fixed':
                    message += f"💰 <b>Type:</b> Fixed Amount\n"
                    message += f"💰 <b>Amount:</b> ₹{link_data['amount_value']} per user\n"
                else:
                    min_amt = link_data['amount_value']['min']
                    max_amt = link_data['amount_value']['max']
                    message += f"🎲 <b>Type:</b> Random Amount\n"
                    message += f"🎲 <b>Range:</b> ₹{min_amt} - ₹{max_amt}\n"

                if link_data['user_limit'] == -1:
                    message += f"👥 <b>User Limit:</b> Unlimited\n"
                else:
                    message += f"👥 <b>User Limit:</b> {link_data['user_limit']} users\n"

                message += f"\n🔗 <b>Link:</b>\n<code>https://t.me/{bot_username}?start=redeem_{code}</code>\n\n"
                message += "📤 <b>Share this link with users to claim rewards!</b>\n\n"
                message += "💡 <b>Usage Instructions:</b>\n"
                message += "• Users click the link to start the bot\n"
                message += "• Rewards are automatically credited to their wallet\n"
                message += "• Each user can only claim once per link"

                keyboard = InlineKeyboardMarkup([
                    [
                        InlineKeyboardButton('🔄 Generate Another', callback_data='link_based_redeem')
                    ],
                    [
                        InlineKeyboardButton('↩️ Go Back', callback_data='manage_gift_codes')
                    ]
                ])

                await update.message.reply_text(message, reply_markup=keyboard, parse_mode='HTML')
            else:
                await update.message.reply_text(
                    "❌ <b>Error</b>\n\nFailed to generate redemption link. Please try again later.",
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _generate_redemption_link: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong while generating the link. Please try again later.",
                parse_mode='HTML'
            )

    async def _handle_create_custom_referral_step2(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle custom referral creation input"""
        user_id = update.effective_user.id
        custom_param = update.message.text.strip()

        try:
            # Validate input
            if not custom_param:
                await update.message.reply_text(
                    "❌ <b>Invalid Input</b>\n\nPlease enter a valid custom name.\n\nSend /cancel to cancel.",
                    parse_mode='HTML'
                )
                return

            # Create custom referral link
            from services.custom_referral_service import CustomReferralService
            custom_referral_service = CustomReferralService()

            result = await custom_referral_service.create_custom_referral(custom_param, user_id, user_id)

            # Clear session
            await self.clear_user_session(user_id)

            if result['success']:
                # Success message
                success_message = (
                    "✅ <b>Custom Referral Link Created Successfully!</b>\n\n"
                    f"🔗 <b>Your Custom Link:</b>\n{result['custom_link']}\n\n"
                    f"📝 <b>Custom Parameter:</b> {result['custom_param']}\n\n"
                    "🎉 You can now share this personalized link to earn referral rewards!\n\n"
                    "💡 <b>Remember:</b> This link cannot be changed, so share it wisely!"
                )

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('📋 Copy Link', url=result['custom_link'])],
                    [InlineKeyboardButton('👥 Share Link', url=f"https://t.me/share/url?text={result['custom_link']}")],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='joined')]
                ])

                await update.message.reply_text(
                    success_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )
            else:
                # Error message
                error_message = f"❌ <b>Failed to Create Custom Link</b>\n\n{result['message']}\n\nPlease try again with a different name."

                from telegram import InlineKeyboardButton, InlineKeyboardMarkup
                keyboard = InlineKeyboardMarkup([
                    [InlineKeyboardButton('🔄 Try Again', callback_data='customReferralLink')],
                    [InlineKeyboardButton('↩️ Go Back', callback_data='joined')]
                ])

                await update.message.reply_text(
                    error_message,
                    reply_markup=keyboard,
                    parse_mode='HTML'
                )

        except Exception as e:
            logger.error(f"Error in _handle_create_custom_referral_step2: {e}")
            await update.message.reply_text(
                "❌ <b>Error</b>\n\nSomething went wrong while creating your custom link. Please try again later.",
                parse_mode='HTML'
            )
            await self.clear_user_session(user_id)
